# Environment
NODE_ENV=development

# Server Configuration
PORT=3001
HOST=0.0.0.0

# Database Configuration
DATABASE_URL=postgresql://postgres:password@localhost:5432/sfquant

# Redis Configuration
REDIS_URL=redis://localhost:6379

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-in-production
JWT_EXPIRES_IN=7d

# CORS Configuration
CORS_ORIGINS=http://localhost:3000,http://localhost:3001

# Rate Limiting
RATE_LIMIT_MAX=100
RATE_LIMIT_WINDOW=1 minute

# Logging
LOG_LEVEL=info

# Encryption
ENCRYPTION_KEY=your-32-character-encryption-key!!

# External APIs (Optional)
COINGECKO_API_KEY=
COINMARKETCAP_API_KEY=

# WebSocket Configuration
WS_HEARTBEAT_INTERVAL=30000
WS_MAX_CONNECTIONS=1000

# Strategy Execution
STRATEGY_TIMEOUT=30000
MAX_CONCURRENT_STRATEGIES=10

# Market Data
MARKET_DATA_CACHE_TTL=60
PRICE_UPDATE_INTERVAL=1000
