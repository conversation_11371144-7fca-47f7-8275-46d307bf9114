import { createClient } from 'redis';
export class RedisService {
    constructor(url) {
        this.url = url;
        this.client = null;
        this.connected = false;
    }
    async connect() {
        try {
            this.client = createClient({
                url: this.url
            });
            this.client.on('error', (error) => {
                console.error('Redis error:', error);
            });
            this.client.on('connect', () => {
                console.log('Redis connected');
                this.connected = true;
            });
            this.client.on('disconnect', () => {
                console.log('Redis disconnected');
                this.connected = false;
            });
            await this.client.connect();
            console.log('Redis service initialized successfully');
        }
        catch (error) {
            console.error('Failed to connect to Redis:', error);
            throw error;
        }
    }
    async disconnect() {
        if (this.client && this.connected) {
            await this.client.disconnect();
            this.client = null;
            this.connected = false;
        }
    }
    isConnected() {
        return this.connected && this.client !== null;
    }
    // 基础操作
    async get(key) {
        if (!this.client)
            throw new Error('Redis client not initialized');
        return await this.client.get(key);
    }
    async set(key, value) {
        if (!this.client)
            throw new Error('Redis client not initialized');
        await this.client.set(key, value);
    }
    async setex(key, seconds, value) {
        if (!this.client)
            throw new Error('Redis client not initialized');
        await this.client.setEx(key, seconds, value);
    }
    async del(key) {
        if (!this.client)
            throw new Error('Redis client not initialized');
        return await this.client.del(key);
    }
    async exists(key) {
        if (!this.client)
            throw new Error('Redis client not initialized');
        return await this.client.exists(key);
    }
    async expire(key, seconds) {
        if (!this.client)
            throw new Error('Redis client not initialized');
        return await this.client.expire(key, seconds);
    }
    async ttl(key) {
        if (!this.client)
            throw new Error('Redis client not initialized');
        return await this.client.ttl(key);
    }
    // 哈希操作
    async hget(key, field) {
        if (!this.client)
            throw new Error('Redis client not initialized');
        return await this.client.hGet(key, field);
    }
    async hset(key, field, value) {
        if (!this.client)
            throw new Error('Redis client not initialized');
        return await this.client.hSet(key, field, value);
    }
    async hgetall(key) {
        if (!this.client)
            throw new Error('Redis client not initialized');
        return await this.client.hGetAll(key);
    }
    async hdel(key, field) {
        if (!this.client)
            throw new Error('Redis client not initialized');
        return await this.client.hDel(key, field);
    }
    // 列表操作
    async lpush(key, ...values) {
        if (!this.client)
            throw new Error('Redis client not initialized');
        return await this.client.lPush(key, values);
    }
    async rpush(key, ...values) {
        if (!this.client)
            throw new Error('Redis client not initialized');
        return await this.client.rPush(key, values);
    }
    async lpop(key) {
        if (!this.client)
            throw new Error('Redis client not initialized');
        return await this.client.lPop(key);
    }
    async rpop(key) {
        if (!this.client)
            throw new Error('Redis client not initialized');
        return await this.client.rPop(key);
    }
    async lrange(key, start, stop) {
        if (!this.client)
            throw new Error('Redis client not initialized');
        return await this.client.lRange(key, start, stop);
    }
    async llen(key) {
        if (!this.client)
            throw new Error('Redis client not initialized');
        return await this.client.lLen(key);
    }
    // 集合操作
    async sadd(key, ...members) {
        if (!this.client)
            throw new Error('Redis client not initialized');
        return await this.client.sAdd(key, members);
    }
    async srem(key, ...members) {
        if (!this.client)
            throw new Error('Redis client not initialized');
        return await this.client.sRem(key, members);
    }
    async smembers(key) {
        if (!this.client)
            throw new Error('Redis client not initialized');
        return await this.client.sMembers(key);
    }
    async sismember(key, member) {
        if (!this.client)
            throw new Error('Redis client not initialized');
        return await this.client.sIsMember(key, member);
    }
    // 有序集合操作
    async zadd(key, score, member) {
        if (!this.client)
            throw new Error('Redis client not initialized');
        return await this.client.zAdd(key, { score, value: member });
    }
    async zrange(key, start, stop) {
        if (!this.client)
            throw new Error('Redis client not initialized');
        return await this.client.zRange(key, start, stop);
    }
    async zrem(key, ...members) {
        if (!this.client)
            throw new Error('Redis client not initialized');
        return await this.client.zRem(key, members);
    }
    // 发布/订阅
    async publish(channel, message) {
        if (!this.client)
            throw new Error('Redis client not initialized');
        return await this.client.publish(channel, message);
    }
    async subscribe(channel, callback) {
        if (!this.client)
            throw new Error('Redis client not initialized');
        const subscriber = this.client.duplicate();
        await subscriber.connect();
        await subscriber.subscribe(channel, (message) => {
            callback(message);
        });
    }
    // 缓存相关的便捷方法
    async cacheMarketData(symbol, data, ttl = 60) {
        const key = `market_data:${symbol}`;
        await this.setex(key, ttl, JSON.stringify(data));
    }
    async getCachedMarketData(symbol) {
        const key = `market_data:${symbol}`;
        const data = await this.get(key);
        return data ? JSON.parse(data) : null;
    }
    async cacheOrderBook(symbol, data, ttl = 30) {
        const key = `orderbook:${symbol}`;
        await this.setex(key, ttl, JSON.stringify(data));
    }
    async getCachedOrderBook(symbol) {
        const key = `orderbook:${symbol}`;
        const data = await this.get(key);
        return data ? JSON.parse(data) : null;
    }
    // 会话管理
    async setSession(sessionId, data, ttl = 3600) {
        const key = `session:${sessionId}`;
        await this.setex(key, ttl, JSON.stringify(data));
    }
    async getSession(sessionId) {
        const key = `session:${sessionId}`;
        const data = await this.get(key);
        return data ? JSON.parse(data) : null;
    }
    async deleteSession(sessionId) {
        const key = `session:${sessionId}`;
        await this.del(key);
    }
    // 健康检查
    async healthCheck() {
        try {
            if (!this.connected || !this.client) {
                return { status: 'error', message: 'Redis not connected' };
            }
            await this.client.ping();
            return { status: 'ok' };
        }
        catch (error) {
            return {
                status: 'error',
                message: error instanceof Error ? error.message : 'Unknown Redis error'
            };
        }
    }
}
//# sourceMappingURL=RedisService.js.map