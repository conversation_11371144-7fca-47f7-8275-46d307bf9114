import { StrategyConfig, StrategyLanguage, Signal } from '@sfquant/core';
import { ValidationResult } from '@sfquant/strategy-runtime';
import { DatabaseService } from './DatabaseService';
import { RedisService } from './RedisService';
export interface StrategyExecution {
    id: string;
    strategyId: string;
    status: 'running' | 'stopped' | 'error';
    startTime: number;
    lastExecution: number;
    executionCount: number;
    signals: Signal[];
    errors: string[];
    metrics: Record<string, number>;
}
export declare class StrategyService {
    private db;
    private redis;
    private runtimeManager;
    private runningStrategies;
    private executionIntervals;
    constructor(db: DatabaseService, redis: RedisService);
    private initializeRuntimes;
    createStrategy(strategy: Omit<StrategyConfig, 'id' | 'createdAt' | 'updatedAt'>): Promise<StrategyConfig>;
    updateStrategy(id: string, updates: Partial<StrategyConfig>): Promise<StrategyConfig>;
    getStrategy(id: string): Promise<StrategyConfig | null>;
    getAllStrategies(): Promise<StrategyConfig[]>;
    deleteStrategy(id: string): Promise<void>;
    validateStrategy(language: StrategyLanguage, code: string): Promise<ValidationResult>;
    startStrategy(id: string): Promise<void>;
    stopStrategy(id: string): Promise<void>;
    private executeStrategy;
    private buildStrategyContext;
    private processStrategySignals;
    getStrategyExecution(id: string): StrategyExecution | undefined;
    getRunningStrategies(): StrategyExecution[];
    getStrategyStats(id: string): Promise<{
        totalExecutions: number;
        totalSignals: number;
        errorRate: number;
        avgExecutionTime: number;
        lastError?: string;
    }>;
    private generateStrategyId;
    private generateExecutionId;
    private saveStrategy;
    private loadStrategy;
    private loadAllStrategies;
    private removeStrategy;
}
//# sourceMappingURL=StrategyService.d.ts.map