import { UnifiedExchange, ExchangeConfig, UnifiedOrder, OrderResult, MarketData, OrderBook, Balance } from '@sfquant/core';
export declare class ExchangeService {
    private exchanges;
    private exchangeConfigs;
    constructor();
    private initializeDefaultExchanges;
    addExchange(exchangeConfig: ExchangeConfig): Promise<void>;
    removeExchange(exchangeId: string): void;
    getExchange(exchangeId: string): UnifiedExchange | undefined;
    getAllExchanges(): Map<string, UnifiedExchange>;
    getExchangeList(): Array<{
        id: string;
        name: string;
        type: 'cex' | 'dex';
        initialized: boolean;
    }>;
    getMarkets(exchangeId: string): Promise<string[]>;
    getTicker(exchangeId: string, symbol: string): Promise<MarketData>;
    getOrderBook(exchangeId: string, symbol: string, limit?: number): Promise<OrderBook>;
    getBalance(exchangeId: string): Promise<Record<string, Balance>>;
    createOrder(exchangeId: string, order: UnifiedOrder): Promise<OrderResult>;
    cancelOrder(exchangeId: string, orderId: string, symbol?: string): Promise<OrderResult>;
    getOpenOrders(exchangeId: string, symbol?: string): Promise<OrderResult[]>;
    getClosedOrders(exchangeId: string, symbol?: string, since?: number, limit?: number): Promise<OrderResult[]>;
    getMultiExchangePrices(symbol: string): Promise<Record<string, MarketData>>;
    findArbitrageOpportunities(symbol: string): Promise<Array<{
        buyExchange: string;
        sellExchange: string;
        buyPrice: number;
        sellPrice: number;
        spread: number;
        spreadPercent: number;
    }>>;
    healthCheck(): Promise<Record<string, {
        status: 'ok' | 'error';
        message?: string;
    }>>;
}
//# sourceMappingURL=ExchangeService.d.ts.map