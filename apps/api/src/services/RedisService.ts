import { createClient, RedisClientType } from 'redis'

export class RedisService {
  private client: RedisClientType | null = null
  private connected: boolean = false

  constructor(private url: string) {}

  async connect(): Promise<void> {
    try {
      this.client = createClient({
        url: this.url
      })

      this.client.on('error', (error) => {
        console.error('Redis error:', error)
      })

      this.client.on('connect', () => {
        console.log('Redis connected')
        this.connected = true
      })

      this.client.on('disconnect', () => {
        console.log('Redis disconnected')
        this.connected = false
      })

      await this.client.connect()
      console.log('Redis service initialized successfully')
    } catch (error) {
      console.error('Failed to connect to Redis:', error)
      throw error
    }
  }

  async disconnect(): Promise<void> {
    if (this.client && this.connected) {
      await this.client.disconnect()
      this.client = null
      this.connected = false
    }
  }

  isConnected(): boolean {
    return this.connected && this.client !== null
  }

  // 基础操作
  async get(key: string): Promise<string | null> {
    if (!this.client) throw new Error('Redis client not initialized')
    return await this.client.get(key)
  }

  async set(key: string, value: string): Promise<void> {
    if (!this.client) throw new Error('Redis client not initialized')
    await this.client.set(key, value)
  }

  async setex(key: string, seconds: number, value: string): Promise<void> {
    if (!this.client) throw new Error('Redis client not initialized')
    await this.client.setEx(key, seconds, value)
  }

  async del(key: string): Promise<number> {
    if (!this.client) throw new Error('Redis client not initialized')
    return await this.client.del(key)
  }

  async exists(key: string): Promise<number> {
    if (!this.client) throw new Error('Redis client not initialized')
    return await this.client.exists(key)
  }

  async expire(key: string, seconds: number): Promise<boolean> {
    if (!this.client) throw new Error('Redis client not initialized')
    return await this.client.expire(key, seconds)
  }

  async ttl(key: string): Promise<number> {
    if (!this.client) throw new Error('Redis client not initialized')
    return await this.client.ttl(key)
  }

  // 哈希操作
  async hget(key: string, field: string): Promise<string | undefined> {
    if (!this.client) throw new Error('Redis client not initialized')
    return await this.client.hGet(key, field)
  }

  async hset(key: string, field: string, value: string): Promise<number> {
    if (!this.client) throw new Error('Redis client not initialized')
    return await this.client.hSet(key, field, value)
  }

  async hgetall(key: string): Promise<Record<string, string>> {
    if (!this.client) throw new Error('Redis client not initialized')
    return await this.client.hGetAll(key)
  }

  async hdel(key: string, field: string): Promise<number> {
    if (!this.client) throw new Error('Redis client not initialized')
    return await this.client.hDel(key, field)
  }

  // 列表操作
  async lpush(key: string, ...values: string[]): Promise<number> {
    if (!this.client) throw new Error('Redis client not initialized')
    return await this.client.lPush(key, values)
  }

  async rpush(key: string, ...values: string[]): Promise<number> {
    if (!this.client) throw new Error('Redis client not initialized')
    return await this.client.rPush(key, values)
  }

  async lpop(key: string): Promise<string | null> {
    if (!this.client) throw new Error('Redis client not initialized')
    return await this.client.lPop(key)
  }

  async rpop(key: string): Promise<string | null> {
    if (!this.client) throw new Error('Redis client not initialized')
    return await this.client.rPop(key)
  }

  async lrange(key: string, start: number, stop: number): Promise<string[]> {
    if (!this.client) throw new Error('Redis client not initialized')
    return await this.client.lRange(key, start, stop)
  }

  async llen(key: string): Promise<number> {
    if (!this.client) throw new Error('Redis client not initialized')
    return await this.client.lLen(key)
  }

  // 集合操作
  async sadd(key: string, ...members: string[]): Promise<number> {
    if (!this.client) throw new Error('Redis client not initialized')
    return await this.client.sAdd(key, members)
  }

  async srem(key: string, ...members: string[]): Promise<number> {
    if (!this.client) throw new Error('Redis client not initialized')
    return await this.client.sRem(key, members)
  }

  async smembers(key: string): Promise<string[]> {
    if (!this.client) throw new Error('Redis client not initialized')
    return await this.client.sMembers(key)
  }

  async sismember(key: string, member: string): Promise<boolean> {
    if (!this.client) throw new Error('Redis client not initialized')
    return await this.client.sIsMember(key, member)
  }

  // 有序集合操作
  async zadd(key: string, score: number, member: string): Promise<number> {
    if (!this.client) throw new Error('Redis client not initialized')
    return await this.client.zAdd(key, { score, value: member })
  }

  async zrange(key: string, start: number, stop: number): Promise<string[]> {
    if (!this.client) throw new Error('Redis client not initialized')
    return await this.client.zRange(key, start, stop)
  }

  async zrem(key: string, ...members: string[]): Promise<number> {
    if (!this.client) throw new Error('Redis client not initialized')
    return await this.client.zRem(key, members)
  }

  // 发布/订阅
  async publish(channel: string, message: string): Promise<number> {
    if (!this.client) throw new Error('Redis client not initialized')
    return await this.client.publish(channel, message)
  }

  async subscribe(channel: string, callback: (message: string) => void): Promise<void> {
    if (!this.client) throw new Error('Redis client not initialized')
    
    const subscriber = this.client.duplicate()
    await subscriber.connect()
    
    await subscriber.subscribe(channel, (message) => {
      callback(message)
    })
  }

  // 缓存相关的便捷方法
  async cacheMarketData(symbol: string, data: any, ttl: number = 60): Promise<void> {
    const key = `market_data:${symbol}`
    await this.setex(key, ttl, JSON.stringify(data))
  }

  async getCachedMarketData(symbol: string): Promise<any | null> {
    const key = `market_data:${symbol}`
    const data = await this.get(key)
    return data ? JSON.parse(data) : null
  }

  async cacheOrderBook(symbol: string, data: any, ttl: number = 30): Promise<void> {
    const key = `orderbook:${symbol}`
    await this.setex(key, ttl, JSON.stringify(data))
  }

  async getCachedOrderBook(symbol: string): Promise<any | null> {
    const key = `orderbook:${symbol}`
    const data = await this.get(key)
    return data ? JSON.parse(data) : null
  }

  // 会话管理
  async setSession(sessionId: string, data: any, ttl: number = 3600): Promise<void> {
    const key = `session:${sessionId}`
    await this.setex(key, ttl, JSON.stringify(data))
  }

  async getSession(sessionId: string): Promise<any | null> {
    const key = `session:${sessionId}`
    const data = await this.get(key)
    return data ? JSON.parse(data) : null
  }

  async deleteSession(sessionId: string): Promise<void> {
    const key = `session:${sessionId}`
    await this.del(key)
  }

  // 健康检查
  async healthCheck(): Promise<{ status: 'ok' | 'error', message?: string }> {
    try {
      if (!this.connected || !this.client) {
        return { status: 'error', message: 'Redis not connected' }
      }

      await this.client.ping()
      return { status: 'ok' }
    } catch (error) {
      return { 
        status: 'error', 
        message: error instanceof Error ? error.message : 'Unknown Redis error'
      }
    }
  }
}
