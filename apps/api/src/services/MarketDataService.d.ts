import { MarketData, OrderBook } from '@sfquant/core';
import { RedisService } from './RedisService';
export interface PriceAlert {
    id: string;
    symbol: string;
    condition: 'above' | 'below';
    price: number;
    userId: string;
    active: boolean;
    createdAt: number;
}
export declare class MarketDataService {
    private redis;
    private priceUpdateIntervals;
    private subscribedSymbols;
    private priceAlerts;
    constructor(redis: RedisService);
    initialize(): Promise<void>;
    subscribeToSymbol(symbol: string): Promise<void>;
    unsubscribeFromSymbol(symbol: string): Promise<void>;
    getMarketData(symbol: string): Promise<MarketData | null>;
    getOrderBook(symbol: string): Promise<OrderBook | null>;
    getMultipleMarketData(symbols: string[]): Promise<Record<string, MarketData>>;
    getPriceHistory(symbol: string, timeframe: string, limit?: number): Promise<any[]>;
    createPriceAlert(alert: Omit<PriceAlert, 'id' | 'createdAt'>): Promise<PriceAlert>;
    deletePriceAlert(alertId: string): Promise<void>;
    getUserPriceAlerts(userId: string): PriceAlert[];
    private checkPriceAlerts;
    private triggerPriceAlert;
    private sendAlertNotification;
    private updateMarketData;
    private updateOrderBook;
    private fetchMarketDataFromAPI;
    private fetchOrderBookFromAPI;
    getMarketStats(): Promise<{
        subscribedSymbols: number;
        activeAlerts: number;
        cacheHitRate: number;
    }>;
    cleanup(): Promise<void>;
    private generateAlertId;
}
//# sourceMappingURL=MarketDataService.d.ts.map