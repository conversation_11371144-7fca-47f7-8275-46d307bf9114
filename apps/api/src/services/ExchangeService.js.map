{"version": 3, "file": "ExchangeService.js", "sourceRoot": "", "sources": ["ExchangeService.ts"], "names": [], "mappings": "AAAA,OAAO,EAGL,aAAa,EAMd,MAAM,eAAe,CAAA;AACtB,OAAO,EAAE,WAAW,EAAE,MAAM,uBAAuB,CAAA;AACnD,OAAO,EAAE,UAAU,EAAE,MAAM,sBAAsB,CAAA;AACjD,OAAO,EAAE,MAAM,EAAE,MAAM,WAAW,CAAA;AAElC,MAAM,OAAO,eAAe;IAI1B;QAHQ,cAAS,GAAiC,IAAI,GAAG,EAAE,CAAA;QACnD,oBAAe,GAAgC,IAAI,GAAG,EAAE,CAAA;QAG9D,IAAI,CAAC,0BAA0B,EAAE,CAAA;IACnC,CAAC;IAED,aAAa;IACL,0BAA0B;QAChC,UAAU;QACV,MAAM,UAAU,GAAqB;YACnC;gBACE,EAAE,EAAE,SAAS;gBACb,IAAI,EAAE,SAAS;gBACf,IAAI,EAAE,KAAK;gBACX,OAAO,EAAE,MAAM,CAAC,QAAQ,KAAK,YAAY;aAC1C;YACD;gBACE,EAAE,EAAE,KAAK;gBACT,IAAI,EAAE,KAAK;gBACX,IAAI,EAAE,KAAK;gBACX,OAAO,EAAE,MAAM,CAAC,QAAQ,KAAK,YAAY;aAC1C;SACF,CAAA;QAED,UAAU;QACV,MAAM,UAAU,GAAqB;YACnC;gBACE,EAAE,EAAE,cAAc;gBAClB,IAAI,EAAE,cAAc;gBACpB,IAAI,EAAE,KAAK;gBACX,OAAO,EAAE,CAAC;gBACV,MAAM,EAAE,0BAA0B;gBAClC,OAAO,EAAE,MAAM,CAAC,QAAQ,KAAK,YAAY;aAC1C;YACD;gBACE,EAAE,EAAE,SAAS;gBACb,IAAI,EAAE,SAAS;gBACf,IAAI,EAAE,KAAK;gBACX,OAAO,EAAE,EAAE;gBACX,MAAM,EAAE,kCAAkC;gBAC1C,OAAO,EAAE,MAAM,CAAC,QAAQ,KAAK,YAAY;aAC1C;SACF;QAED,OAAO;SACN,CAAA,EAAG,UAAU,EAAK,UAAU,EAAE,OAAO,CAAA;QAAA,CAAC,MAAM,CAAC,EAAE;YAC9C,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,EAAE,MAAM,CAAC,CAAA;QAC7C,CAAC,CAAC,CAAA;IACJ,CAAC;IAED,QAAQ;IACR,KAAK,CAAC,WAAW,CAAC,cAA8B;QAC9C,IAAI,CAAC;YACH,IAAI,QAAyB,CAAA;YAE7B,IAAI,cAAc,CAAC,IAAI,KAAK,KAAK,EAAE,CAAC;gBAClC,QAAQ,GAAG,IAAI,WAAW,EAAE,CAAA;YAC9B,CAAC;iBAAM,CAAC;gBACN,QAAQ,GAAG,IAAI,UAAU,EAAE,CAAA;YAC7B,CAAC;YAED,MAAM,QAAQ,CAAC,UAAU,CAAC,cAAc,CAAC,CAAA;YAEzC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,cAAc,CAAC,EAAE,EAAE,QAAQ,CAAC,CAAA;YAC/C,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,cAAc,CAAC,EAAE,EAAE,cAAc,CAAC,CAAA;YAE3D,OAAO,CAAC,GAAG,CAAC,YAAY,cAAc,CAAC,IAAI,2BAA2B,CAAC,CAAA;QACzE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,aAAa,CACrB,iCAAiC,cAAc,CAAC,IAAI,KAAK,KAAK,EAAE,EAChE,cAAc,CAAC,EAAE,CAClB,CAAA;QACH,CAAC;IACH,CAAC;IAED,QAAQ;IACR,cAAc,CAAC,UAAkB;QAC/B,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,UAAU,CAAC,CAAA;QACjC,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,UAAU,CAAC,CAAA;IACzC,CAAC;IAED,QAAQ;IACR,WAAW,CAAC,UAAkB;QAC5B,OAAO,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,UAAU,CAAC,CAAA;IACvC,CAAC;IAED,UAAU;IACV,eAAe;QACb,OAAO,IAAI,GAAG,CAAC,IAAI,CAAC,SAAS,CAAC,CAAA;IAChC,CAAC;IAED,UAAU;IACV,eAAe;QACb,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,OAAO,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,MAAM,CAAC,EAAE,EAAE,CAAC,CAAC;YACvE,EAAE;YACF,IAAI,EAAE,MAAM,CAAC,IAAI;YACjB,IAAI,EAAE,MAAM,CAAC,IAAI;YACjB,WAAW,EAAE,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,EAAE,CAAC;SACpC,CAAC,CAAC,CAAA;IACL,CAAC;IAED,UAAU;IACV,KAAK,CAAC,UAAU,CAAC,UAAkB;QACjC,MAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,CAAA;QAC7C,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,MAAM,IAAI,aAAa,CAAC,YAAY,UAAU,YAAY,EAAE,UAAU,CAAC,CAAA;QACzE,CAAC;QAED,OAAO,MAAM,QAAQ,CAAC,YAAY,EAAE,CAAA;IACtC,CAAC;IAED,SAAS;IACT,KAAK,CAAC,SAAS,CAAC,UAAkB,EAAE,MAAc;QAChD,MAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,CAAA;QAC7C,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,MAAM,IAAI,aAAa,CAAC,YAAY,UAAU,YAAY,EAAE,UAAU,CAAC,CAAA;QACzE,CAAC;QAED,OAAO,MAAM,QAAQ,CAAC,WAAW,CAAC,MAAM,CAAC,CAAA;IAC3C,CAAC;IAED,QAAQ;IACR,KAAK,CAAC,YAAY,CAAC,UAAkB,EAAE,MAAc,EAAE,KAAc;QACnE,MAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,CAAA;QAC7C,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,MAAM,IAAI,aAAa,CAAC,YAAY,UAAU,YAAY,EAAE,UAAU,CAAC,CAAA;QACzE,CAAC;QAED,OAAO,MAAM,QAAQ,CAAC,cAAc,CAAC,MAAM,EAAE,KAAK,CAAC,CAAA;IACrD,CAAC;IAED,SAAS;IACT,KAAK,CAAC,UAAU,CAAC,UAAkB;QACjC,MAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,CAAA;QAC7C,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,MAAM,IAAI,aAAa,CAAC,YAAY,UAAU,YAAY,EAAE,UAAU,CAAC,CAAA;QACzE,CAAC;QAED,OAAO,MAAM,QAAQ,CAAC,YAAY,EAAE,CAAA;IACtC,CAAC;IAED,OAAO;IACP,KAAK,CAAC,WAAW,CAAC,UAAkB,EAAE,KAAmB;QACvD,MAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,CAAA;QAC7C,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,MAAM,IAAI,aAAa,CAAC,YAAY,UAAU,YAAY,EAAE,UAAU,CAAC,CAAA;QACzE,CAAC;QAED,OAAO,MAAM,QAAQ,CAAC,WAAW,CAAC,KAAK,CAAC,CAAA;IAC1C,CAAC;IAED,OAAO;IACP,KAAK,CAAC,WAAW,CAAC,UAAkB,EAAE,OAAe,EAAE,MAAe;QACpE,MAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,CAAA;QAC7C,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,MAAM,IAAI,aAAa,CAAC,YAAY,UAAU,YAAY,EAAE,UAAU,CAAC,CAAA;QACzE,CAAC;QAED,OAAO,MAAM,QAAQ,CAAC,WAAW,CAAC,OAAO,EAAE,MAAM,CAAC,CAAA;IACpD,CAAC;IAED,UAAU;IACV,KAAK,CAAC,aAAa,CAAC,UAAkB,EAAE,MAAe;QACrD,MAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,CAAA;QAC7C,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,MAAM,IAAI,aAAa,CAAC,YAAY,UAAU,YAAY,EAAE,UAAU,CAAC,CAAA;QACzE,CAAC;QAED,OAAO,MAAM,QAAQ,CAAC,eAAe,CAAC,MAAM,CAAC,CAAA;IAC/C,CAAC;IAED,SAAS;IACT,KAAK,CAAC,eAAe,CACnB,UAAkB,EAClB,MAAe,EACf,KAAc,EACd,KAAc;QAEd,MAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,CAAA;QAC7C,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,MAAM,IAAI,aAAa,CAAC,YAAY,UAAU,YAAY,EAAE,UAAU,CAAC,CAAA;QACzE,CAAC;QAED,OAAO,MAAM,QAAQ,CAAC,iBAAiB,CAAC,MAAM,EAAE,KAAK,EAAE,KAAK,CAAC,CAAA;IAC/D,CAAC;IAED,eAAe;IACf,KAAK,CAAC,sBAAsB,CAAC,MAAc;QACzC,MAAM,MAAM,GAA+B,EAAE,CAAA;QAC7C,MAAM,QAAQ,GAAoB,EAAE,CAAA;QAEpC,KAAK,MAAM,CAAC,UAAU,EAAE,QAAQ,CAAC,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YACpD,QAAQ,CAAC,IAAI,CACX,QAAQ,CAAC,WAAW,CAAC,MAAM,CAAC;iBACzB,IAAI,CAAC,MAAM,CAAC,EAAE;gBACb,MAAM,CAAC,UAAU,CAAC,GAAG,MAAM,CAAA;YAC7B,CAAC,CAAC;iBACD,KAAK,CAAC,KAAK,CAAC,EAAE;gBACb,OAAO,CAAC,IAAI,CAAC,4BAA4B,UAAU,GAAG,EAAE,KAAK,CAAC,OAAO,CAAC,CAAA;YACxE,CAAC,CAAC,CACL,CAAA;QACH,CAAC;QAED,MAAM,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAA;QAC3B,OAAO,MAAM,CAAA;IACf,CAAC;IAED,SAAS;IACT,KAAK,CAAC,0BAA0B,CAAC,MAAc;QAQ7C,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,sBAAsB,CAAC,MAAM,CAAC,CAAA;QACxD,MAAM,aAAa,GAOd,EAAE,CAAA;QAEP,MAAM,SAAS,GAAG,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA;QAErC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YAC1C,KAAK,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;gBAC9C,MAAM,SAAS,GAAG,SAAS,CAAC,CAAC,CAAC,CAAA;gBAC9B,MAAM,SAAS,GAAG,SAAS,CAAC,CAAC,CAAC,CAAA;gBAE9B,MAAM,MAAM,GAAG,MAAM,CAAC,SAAS,CAAC,CAAA;gBAChC,MAAM,MAAM,GAAG,MAAM,CAAC,SAAS,CAAC,CAAA;gBAEhC,IAAI,CAAC,MAAM,IAAI,CAAC,MAAM;oBAAE,SAAQ;gBAEhC,6BAA6B;gBAC7B,IAAI,MAAM,CAAC,GAAG,GAAG,MAAM,CAAC,GAAG,EAAE,CAAC;oBAC5B,MAAM,MAAM,GAAG,MAAM,CAAC,GAAG,GAAG,MAAM,CAAC,GAAG,CAAA;oBACtC,MAAM,aAAa,GAAG,CAAC,MAAM,GAAG,MAAM,CAAC,GAAG,CAAC,GAAG,GAAG,CAAA;oBAEjD,aAAa,CAAC,IAAI,CAAC;wBACjB,WAAW,EAAE,SAAS;wBACtB,YAAY,EAAE,SAAS;wBACvB,QAAQ,EAAE,MAAM,CAAC,GAAG;wBACpB,SAAS,EAAE,MAAM,CAAC,GAAG;wBACrB,MAAM;wBACN,aAAa;qBACd,CAAC,CAAA;gBACJ,CAAC;gBAED,6BAA6B;gBAC7B,IAAI,MAAM,CAAC,GAAG,GAAG,MAAM,CAAC,GAAG,EAAE,CAAC;oBAC5B,MAAM,MAAM,GAAG,MAAM,CAAC,GAAG,GAAG,MAAM,CAAC,GAAG,CAAA;oBACtC,MAAM,aAAa,GAAG,CAAC,MAAM,GAAG,MAAM,CAAC,GAAG,CAAC,GAAG,GAAG,CAAA;oBAEjD,aAAa,CAAC,IAAI,CAAC;wBACjB,WAAW,EAAE,SAAS;wBACtB,YAAY,EAAE,SAAS;wBACvB,QAAQ,EAAE,MAAM,CAAC,GAAG;wBACpB,SAAS,EAAE,MAAM,CAAC,GAAG;wBACrB,MAAM;wBACN,aAAa;qBACd,CAAC,CAAA;gBACJ,CAAC;YACH,CAAC;QACH,CAAC;QAED,SAAS;QACT,OAAO,aAAa,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,aAAa,GAAG,CAAC,CAAC,aAAa,CAAC,CAAA;IACxE,CAAC;IAED,OAAO;IACP,KAAK,CAAC,WAAW;QACf,MAAM,OAAO,GAA+D,EAAE,CAAA;QAE9E,KAAK,MAAM,CAAC,UAAU,EAAE,QAAQ,CAAC,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YACpD,IAAI,CAAC;gBACH,IAAI,QAAQ,CAAC,aAAa,EAAE,EAAE,CAAC;oBAC7B,gBAAgB;oBAChB,MAAM,QAAQ,CAAC,YAAY,EAAE,CAAA;oBAC7B,OAAO,CAAC,UAAU,CAAC,GAAG,EAAE,MAAM,EAAE,IAAI,EAAE,CAAA;gBACxC,CAAC;qBAAM,CAAC;oBACN,OAAO,CAAC,UAAU,CAAC,GAAG,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,iBAAiB,EAAE,CAAA;gBACvE,CAAC;YACH,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,OAAO,CAAC,UAAU,CAAC,GAAG;oBACpB,MAAM,EAAE,OAAO;oBACf,OAAO,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe;iBAClE,CAAA;YACH,CAAC;QACH,CAAC;QAED,OAAO,OAAO,CAAA;IAChB,CAAC;CACF"}