import { z } from 'zod';
const createAlertSchema = z.object({
    symbol: z.string().min(1),
    condition: z.enum(['above', 'below']),
    price: z.number().positive(),
    userId: z.string().min(1),
    active: z.boolean().default(true)
});
export async function marketDataRoutes(fastify) {
    // 获取市场数据
    fastify.get('/ticker/:symbol', {
        schema: {
            description: 'Get market data for a symbol',
            tags: ['market-data'],
            params: {
                type: 'object',
                properties: {
                    symbol: { type: 'string' }
                },
                required: ['symbol']
            },
            response: {
                200: {
                    type: 'object',
                    properties: {
                        data: { type: 'object' }
                    }
                }
            }
        }
    }, async (request, reply) => {
        try {
            const { symbol } = request.params;
            const data = await fastify.marketDataService.getMarketData(symbol);
            if (!data) {
                reply.status(404).send({
                    error: 'Not Found',
                    message: `Market data for ${symbol} not found`
                });
                return;
            }
            return { data };
        }
        catch (error) {
            reply.status(500).send({
                error: 'Internal Server Error',
                message: error instanceof Error ? error.message : 'Unknown error'
            });
        }
    });
    // 获取订单簿
    fastify.get('/orderbook/:symbol', {
        schema: {
            description: 'Get order book for a symbol',
            tags: ['market-data'],
            params: {
                type: 'object',
                properties: {
                    symbol: { type: 'string' }
                },
                required: ['symbol']
            }
        }
    }, async (request, reply) => {
        try {
            const { symbol } = request.params;
            const orderBook = await fastify.marketDataService.getOrderBook(symbol);
            if (!orderBook) {
                reply.status(404).send({
                    error: 'Not Found',
                    message: `Order book for ${symbol} not found`
                });
                return;
            }
            return { orderBook };
        }
        catch (error) {
            reply.status(500).send({
                error: 'Internal Server Error',
                message: error instanceof Error ? error.message : 'Unknown error'
            });
        }
    });
    // 获取多个交易对的市场数据
    fastify.post('/ticker/batch', {
        schema: {
            description: 'Get market data for multiple symbols',
            tags: ['market-data'],
            body: {
                type: 'object',
                required: ['symbols'],
                properties: {
                    symbols: {
                        type: 'array',
                        items: { type: 'string' },
                        minItems: 1,
                        maxItems: 50
                    }
                }
            }
        }
    }, async (request, reply) => {
        try {
            const { symbols } = request.body;
            const data = await fastify.marketDataService.getMultipleMarketData(symbols);
            return { data };
        }
        catch (error) {
            reply.status(500).send({
                error: 'Internal Server Error',
                message: error instanceof Error ? error.message : 'Unknown error'
            });
        }
    });
    // 获取价格历史
    fastify.get('/history/:symbol', {
        schema: {
            description: 'Get price history for a symbol',
            tags: ['market-data'],
            params: {
                type: 'object',
                properties: {
                    symbol: { type: 'string' }
                },
                required: ['symbol']
            },
            querystring: {
                type: 'object',
                properties: {
                    timeframe: { type: 'string', enum: ['1m', '5m', '15m', '1h', '4h', '1d'], default: '1h' },
                    limit: { type: 'number', minimum: 1, maximum: 1000, default: 100 }
                }
            }
        }
    }, async (request, reply) => {
        try {
            const { symbol } = request.params;
            const { timeframe = '1h', limit = 100 } = request.query;
            const history = await fastify.marketDataService.getPriceHistory(symbol, timeframe, limit);
            return { history };
        }
        catch (error) {
            reply.status(500).send({
                error: 'Internal Server Error',
                message: error instanceof Error ? error.message : 'Unknown error'
            });
        }
    });
    // 创建价格提醒
    fastify.post('/alerts', {
        schema: {
            description: 'Create a price alert',
            tags: ['market-data'],
            body: {
                type: 'object',
                required: ['symbol', 'condition', 'price', 'userId'],
                properties: {
                    symbol: { type: 'string' },
                    condition: { type: 'string', enum: ['above', 'below'] },
                    price: { type: 'number' },
                    userId: { type: 'string' },
                    active: { type: 'boolean' }
                }
            }
        }
    }, async (request, reply) => {
        try {
            const alertData = createAlertSchema.parse(request.body);
            const alert = await fastify.marketDataService.createPriceAlert(alertData);
            reply.status(201).send({ alert });
        }
        catch (error) {
            if (error instanceof z.ZodError) {
                reply.status(400).send({
                    error: 'Validation Error',
                    message: 'Invalid request body',
                    details: error.errors
                });
            }
            else {
                reply.status(500).send({
                    error: 'Internal Server Error',
                    message: error instanceof Error ? error.message : 'Unknown error'
                });
            }
        }
    });
    // 获取用户的价格提醒
    fastify.get('/alerts/:userId', {
        schema: {
            description: 'Get user price alerts',
            tags: ['market-data'],
            params: {
                type: 'object',
                properties: {
                    userId: { type: 'string' }
                },
                required: ['userId']
            }
        }
    }, async (request, reply) => {
        try {
            const { userId } = request.params;
            const alerts = fastify.marketDataService.getUserPriceAlerts(userId);
            return { alerts };
        }
        catch (error) {
            reply.status(500).send({
                error: 'Internal Server Error',
                message: error instanceof Error ? error.message : 'Unknown error'
            });
        }
    });
    // 删除价格提醒
    fastify.delete('/alerts/:alertId', {
        schema: {
            description: 'Delete a price alert',
            tags: ['market-data'],
            params: {
                type: 'object',
                properties: {
                    alertId: { type: 'string' }
                },
                required: ['alertId']
            }
        }
    }, async (request, reply) => {
        try {
            const { alertId } = request.params;
            await fastify.marketDataService.deletePriceAlert(alertId);
            reply.status(204).send();
        }
        catch (error) {
            reply.status(500).send({
                error: 'Internal Server Error',
                message: error instanceof Error ? error.message : 'Unknown error'
            });
        }
    });
    // 订阅交易对价格更新
    fastify.post('/subscribe/:symbol', {
        schema: {
            description: 'Subscribe to symbol price updates',
            tags: ['market-data'],
            params: {
                type: 'object',
                properties: {
                    symbol: { type: 'string' }
                },
                required: ['symbol']
            }
        }
    }, async (request, reply) => {
        try {
            const { symbol } = request.params;
            await fastify.marketDataService.subscribeToSymbol(symbol);
            return { message: `Subscribed to ${symbol} price updates` };
        }
        catch (error) {
            reply.status(500).send({
                error: 'Internal Server Error',
                message: error instanceof Error ? error.message : 'Unknown error'
            });
        }
    });
    // 取消订阅交易对价格更新
    fastify.delete('/subscribe/:symbol', {
        schema: {
            description: 'Unsubscribe from symbol price updates',
            tags: ['market-data'],
            params: {
                type: 'object',
                properties: {
                    symbol: { type: 'string' }
                },
                required: ['symbol']
            }
        }
    }, async (request, reply) => {
        try {
            const { symbol } = request.params;
            await fastify.marketDataService.unsubscribeFromSymbol(symbol);
            return { message: `Unsubscribed from ${symbol} price updates` };
        }
        catch (error) {
            reply.status(500).send({
                error: 'Internal Server Error',
                message: error instanceof Error ? error.message : 'Unknown error'
            });
        }
    });
    // 获取市场数据统计
    fastify.get('/stats', {
        schema: {
            description: 'Get market data service statistics',
            tags: ['market-data']
        }
    }, async (request, reply) => {
        try {
            const stats = await fastify.marketDataService.getMarketStats();
            return { stats };
        }
        catch (error) {
            reply.status(500).send({
                error: 'Internal Server Error',
                message: error instanceof Error ? error.message : 'Unknown error'
            });
        }
    });
}
//# sourceMappingURL=marketData.js.map