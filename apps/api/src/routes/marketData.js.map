{"version": 3, "file": "marketData.js", "sourceRoot": "", "sources": ["marketData.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,CAAC,EAAE,MAAM,KAAK,CAAA;AAEvB,MAAM,iBAAiB,GAAG,CAAC,CAAC,MAAM,CAAC;IACjC,MAAM,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC;IACzB,SAAS,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;IACrC,KAAK,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;IAC5B,MAAM,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC;IACzB,MAAM,EAAE,CAAC,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC;CAClC,CAAC,CAAA;AAEF,MAAM,CAAC,KAAK,UAAU,gBAAgB,CAAC,OAAwB;IAC7D,SAAS;IACT,OAAO,CAAC,GAAG,CAAC,iBAAiB,EAAE;QAC7B,MAAM,EAAE;YACN,WAAW,EAAE,8BAA8B;YAC3C,IAAI,EAAE,CAAC,aAAa,CAAC;YACrB,MAAM,EAAE;gBACN,IAAI,EAAE,QAAQ;gBACd,UAAU,EAAE;oBACV,MAAM,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;iBAC3B;gBACD,QAAQ,EAAE,CAAC,QAAQ,CAAC;aACrB;YACD,QAAQ,EAAE;gBACR,GAAG,EAAE;oBACH,IAAI,EAAE,QAAQ;oBACd,UAAU,EAAE;wBACV,IAAI,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;qBACzB;iBACF;aACF;SACF;KACF,EAAE,KAAK,EAAE,OAAuD,EAAE,KAAmB,EAAE,EAAE;QACxF,IAAI,CAAC;YACH,MAAM,EAAE,MAAM,EAAE,GAAG,OAAO,CAAC,MAAM,CAAA;YACjC,MAAM,IAAI,GAAG,MAAM,OAAO,CAAC,iBAAiB,CAAC,aAAa,CAAC,MAAM,CAAC,CAAA;YAElE,IAAI,CAAC,IAAI,EAAE,CAAC;gBACV,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;oBACrB,KAAK,EAAE,WAAW;oBAClB,OAAO,EAAE,mBAAmB,MAAM,YAAY;iBAC/C,CAAC,CAAA;gBACF,OAAM;YACR,CAAC;YAED,OAAO,EAAE,IAAI,EAAE,CAAA;QACjB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBACrB,KAAK,EAAE,uBAAuB;gBAC9B,OAAO,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe;aAClE,CAAC,CAAA;QACJ,CAAC;IACH,CAAC,CAAC,CAAA;IAEF,QAAQ;IACR,OAAO,CAAC,GAAG,CAAC,oBAAoB,EAAE;QAChC,MAAM,EAAE;YACN,WAAW,EAAE,6BAA6B;YAC1C,IAAI,EAAE,CAAC,aAAa,CAAC;YACrB,MAAM,EAAE;gBACN,IAAI,EAAE,QAAQ;gBACd,UAAU,EAAE;oBACV,MAAM,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;iBAC3B;gBACD,QAAQ,EAAE,CAAC,QAAQ,CAAC;aACrB;SACF;KACF,EAAE,KAAK,EAAE,OAAuD,EAAE,KAAmB,EAAE,EAAE;QACxF,IAAI,CAAC;YACH,MAAM,EAAE,MAAM,EAAE,GAAG,OAAO,CAAC,MAAM,CAAA;YACjC,MAAM,SAAS,GAAG,MAAM,OAAO,CAAC,iBAAiB,CAAC,YAAY,CAAC,MAAM,CAAC,CAAA;YAEtE,IAAI,CAAC,SAAS,EAAE,CAAC;gBACf,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;oBACrB,KAAK,EAAE,WAAW;oBAClB,OAAO,EAAE,kBAAkB,MAAM,YAAY;iBAC9C,CAAC,CAAA;gBACF,OAAM;YACR,CAAC;YAED,OAAO,EAAE,SAAS,EAAE,CAAA;QACtB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBACrB,KAAK,EAAE,uBAAuB;gBAC9B,OAAO,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe;aAClE,CAAC,CAAA;QACJ,CAAC;IACH,CAAC,CAAC,CAAA;IAEF,eAAe;IACf,OAAO,CAAC,IAAI,CAAC,eAAe,EAAE;QAC5B,MAAM,EAAE;YACN,WAAW,EAAE,sCAAsC;YACnD,IAAI,EAAE,CAAC,aAAa,CAAC;YACrB,IAAI,EAAE;gBACJ,IAAI,EAAE,QAAQ;gBACd,QAAQ,EAAE,CAAC,SAAS,CAAC;gBACrB,UAAU,EAAE;oBACV,OAAO,EAAE;wBACP,IAAI,EAAE,OAAO;wBACb,KAAK,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;wBACzB,QAAQ,EAAE,CAAC;wBACX,QAAQ,EAAE,EAAE;qBACb;iBACF;aACF;SACF;KACF,EAAE,KAAK,EAAE,OAAwD,EAAE,KAAmB,EAAE,EAAE;QACzF,IAAI,CAAC;YACH,MAAM,EAAE,OAAO,EAAE,GAAG,OAAO,CAAC,IAAI,CAAA;YAChC,MAAM,IAAI,GAAG,MAAM,OAAO,CAAC,iBAAiB,CAAC,qBAAqB,CAAC,OAAO,CAAC,CAAA;YAC3E,OAAO,EAAE,IAAI,EAAE,CAAA;QACjB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBACrB,KAAK,EAAE,uBAAuB;gBAC9B,OAAO,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe;aAClE,CAAC,CAAA;QACJ,CAAC;IACH,CAAC,CAAC,CAAA;IAEF,SAAS;IACT,OAAO,CAAC,GAAG,CAAC,kBAAkB,EAAE;QAC9B,MAAM,EAAE;YACN,WAAW,EAAE,gCAAgC;YAC7C,IAAI,EAAE,CAAC,aAAa,CAAC;YACrB,MAAM,EAAE;gBACN,IAAI,EAAE,QAAQ;gBACd,UAAU,EAAE;oBACV,MAAM,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;iBAC3B;gBACD,QAAQ,EAAE,CAAC,QAAQ,CAAC;aACrB;YACD,WAAW,EAAE;gBACX,IAAI,EAAE,QAAQ;gBACd,UAAU,EAAE;oBACV,SAAS,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,EAAE,OAAO,EAAE,IAAI,EAAE;oBACzF,KAAK,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,OAAO,EAAE,CAAC,EAAE,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,EAAE;iBACnE;aACF;SACF;KACF,EAAE,KAAK,EAAE,OAGR,EAAE,KAAmB,EAAE,EAAE;QACzB,IAAI,CAAC;YACH,MAAM,EAAE,MAAM,EAAE,GAAG,OAAO,CAAC,MAAM,CAAA;YACjC,MAAM,EAAE,SAAS,GAAG,IAAI,EAAE,KAAK,GAAG,GAAG,EAAE,GAAG,OAAO,CAAC,KAAK,CAAA;YAEvD,MAAM,OAAO,GAAG,MAAM,OAAO,CAAC,iBAAiB,CAAC,eAAe,CAAC,MAAM,EAAE,SAAS,EAAE,KAAK,CAAC,CAAA;YACzF,OAAO,EAAE,OAAO,EAAE,CAAA;QACpB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBACrB,KAAK,EAAE,uBAAuB;gBAC9B,OAAO,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe;aAClE,CAAC,CAAA;QACJ,CAAC;IACH,CAAC,CAAC,CAAA;IAEF,SAAS;IACT,OAAO,CAAC,IAAI,CAAC,SAAS,EAAE;QACtB,MAAM,EAAE;YACN,WAAW,EAAE,sBAAsB;YACnC,IAAI,EAAE,CAAC,aAAa,CAAC;YACrB,IAAI,EAAE;gBACJ,IAAI,EAAE,QAAQ;gBACd,QAAQ,EAAE,CAAC,QAAQ,EAAE,WAAW,EAAE,OAAO,EAAE,QAAQ,CAAC;gBACpD,UAAU,EAAE;oBACV,MAAM,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;oBAC1B,SAAS,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC,EAAE;oBACvD,KAAK,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;oBACzB,MAAM,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;oBAC1B,MAAM,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE;iBAC5B;aACF;SACF;KACF,EAAE,KAAK,EAAE,OAAuB,EAAE,KAAmB,EAAE,EAAE;QACxD,IAAI,CAAC;YACH,MAAM,SAAS,GAAG,iBAAiB,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAA;YACvD,MAAM,KAAK,GAAG,MAAM,OAAO,CAAC,iBAAiB,CAAC,gBAAgB,CAAC,SAAS,CAAC,CAAA;YAEzE,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,CAAC,CAAA;QACnC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,KAAK,YAAY,CAAC,CAAC,QAAQ,EAAE,CAAC;gBAChC,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;oBACrB,KAAK,EAAE,kBAAkB;oBACzB,OAAO,EAAE,sBAAsB;oBAC/B,OAAO,EAAE,KAAK,CAAC,MAAM;iBACtB,CAAC,CAAA;YACJ,CAAC;iBAAM,CAAC;gBACN,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;oBACrB,KAAK,EAAE,uBAAuB;oBAC9B,OAAO,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe;iBAClE,CAAC,CAAA;YACJ,CAAC;QACH,CAAC;IACH,CAAC,CAAC,CAAA;IAEF,YAAY;IACZ,OAAO,CAAC,GAAG,CAAC,iBAAiB,EAAE;QAC7B,MAAM,EAAE;YACN,WAAW,EAAE,uBAAuB;YACpC,IAAI,EAAE,CAAC,aAAa,CAAC;YACrB,MAAM,EAAE;gBACN,IAAI,EAAE,QAAQ;gBACd,UAAU,EAAE;oBACV,MAAM,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;iBAC3B;gBACD,QAAQ,EAAE,CAAC,QAAQ,CAAC;aACrB;SACF;KACF,EAAE,KAAK,EAAE,OAAuD,EAAE,KAAmB,EAAE,EAAE;QACxF,IAAI,CAAC;YACH,MAAM,EAAE,MAAM,EAAE,GAAG,OAAO,CAAC,MAAM,CAAA;YACjC,MAAM,MAAM,GAAG,OAAO,CAAC,iBAAiB,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAA;YACnE,OAAO,EAAE,MAAM,EAAE,CAAA;QACnB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBACrB,KAAK,EAAE,uBAAuB;gBAC9B,OAAO,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe;aAClE,CAAC,CAAA;QACJ,CAAC;IACH,CAAC,CAAC,CAAA;IAEF,SAAS;IACT,OAAO,CAAC,MAAM,CAAC,kBAAkB,EAAE;QACjC,MAAM,EAAE;YACN,WAAW,EAAE,sBAAsB;YACnC,IAAI,EAAE,CAAC,aAAa,CAAC;YACrB,MAAM,EAAE;gBACN,IAAI,EAAE,QAAQ;gBACd,UAAU,EAAE;oBACV,OAAO,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;iBAC5B;gBACD,QAAQ,EAAE,CAAC,SAAS,CAAC;aACtB;SACF;KACF,EAAE,KAAK,EAAE,OAAwD,EAAE,KAAmB,EAAE,EAAE;QACzF,IAAI,CAAC;YACH,MAAM,EAAE,OAAO,EAAE,GAAG,OAAO,CAAC,MAAM,CAAA;YAClC,MAAM,OAAO,CAAC,iBAAiB,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAA;YAEzD,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAA;QAC1B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBACrB,KAAK,EAAE,uBAAuB;gBAC9B,OAAO,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe;aAClE,CAAC,CAAA;QACJ,CAAC;IACH,CAAC,CAAC,CAAA;IAEF,YAAY;IACZ,OAAO,CAAC,IAAI,CAAC,oBAAoB,EAAE;QACjC,MAAM,EAAE;YACN,WAAW,EAAE,mCAAmC;YAChD,IAAI,EAAE,CAAC,aAAa,CAAC;YACrB,MAAM,EAAE;gBACN,IAAI,EAAE,QAAQ;gBACd,UAAU,EAAE;oBACV,MAAM,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;iBAC3B;gBACD,QAAQ,EAAE,CAAC,QAAQ,CAAC;aACrB;SACF;KACF,EAAE,KAAK,EAAE,OAAuD,EAAE,KAAmB,EAAE,EAAE;QACxF,IAAI,CAAC;YACH,MAAM,EAAE,MAAM,EAAE,GAAG,OAAO,CAAC,MAAM,CAAA;YACjC,MAAM,OAAO,CAAC,iBAAiB,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAA;YAEzD,OAAO,EAAE,OAAO,EAAE,iBAAiB,MAAM,gBAAgB,EAAE,CAAA;QAC7D,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBACrB,KAAK,EAAE,uBAAuB;gBAC9B,OAAO,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe;aAClE,CAAC,CAAA;QACJ,CAAC;IACH,CAAC,CAAC,CAAA;IAEF,cAAc;IACd,OAAO,CAAC,MAAM,CAAC,oBAAoB,EAAE;QACnC,MAAM,EAAE;YACN,WAAW,EAAE,uCAAuC;YACpD,IAAI,EAAE,CAAC,aAAa,CAAC;YACrB,MAAM,EAAE;gBACN,IAAI,EAAE,QAAQ;gBACd,UAAU,EAAE;oBACV,MAAM,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;iBAC3B;gBACD,QAAQ,EAAE,CAAC,QAAQ,CAAC;aACrB;SACF;KACF,EAAE,KAAK,EAAE,OAAuD,EAAE,KAAmB,EAAE,EAAE;QACxF,IAAI,CAAC;YACH,MAAM,EAAE,MAAM,EAAE,GAAG,OAAO,CAAC,MAAM,CAAA;YACjC,MAAM,OAAO,CAAC,iBAAiB,CAAC,qBAAqB,CAAC,MAAM,CAAC,CAAA;YAE7D,OAAO,EAAE,OAAO,EAAE,qBAAqB,MAAM,gBAAgB,EAAE,CAAA;QACjE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBACrB,KAAK,EAAE,uBAAuB;gBAC9B,OAAO,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe;aAClE,CAAC,CAAA;QACJ,CAAC;IACH,CAAC,CAAC,CAAA;IAEF,WAAW;IACX,OAAO,CAAC,GAAG,CAAC,QAAQ,EAAE;QACpB,MAAM,EAAE;YACN,WAAW,EAAE,oCAAoC;YACjD,IAAI,EAAE,CAAC,aAAa,CAAC;SACtB;KACF,EAAE,KAAK,EAAE,OAAuB,EAAE,KAAmB,EAAE,EAAE;QACxD,IAAI,CAAC;YACH,MAAM,KAAK,GAAG,MAAM,OAAO,CAAC,iBAAiB,CAAC,cAAc,EAAE,CAAA;YAC9D,OAAO,EAAE,KAAK,EAAE,CAAA;QAClB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBACrB,KAAK,EAAE,uBAAuB;gBAC9B,OAAO,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe;aAClE,CAAC,CAAA;QACJ,CAAC;IACH,CAAC,CAAC,CAAA;AACJ,CAAC"}