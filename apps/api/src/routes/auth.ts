import { FastifyInstance, FastifyRequest, FastifyReply } from 'fastify'
import { z } from 'zod'
import bcrypt from 'bcryptjs'
import jwt from 'jsonwebtoken'
import { config } from '../config'

// 请求验证schemas
const registerSchema = z.object({
  email: z.string().email(),
  password: z.string().min(8),
  name: z.string().min(1).max(100)
})

const loginSchema = z.object({
  email: z.string().email(),
  password: z.string().min(1)
})

const refreshTokenSchema = z.object({
  refreshToken: z.string()
})

// JWT认证中间件
async function authenticate(request: FastifyRequest, reply: FastifyReply) {
  try {
    await request.jwtVerify()
  } catch (error) {
    reply.status(401).send({
      error: 'Unauthorized',
      message: 'Invalid or missing token'
    })
  }
}

export async function authRoutes(fastify: FastifyInstance): Promise<void> {
  // 用户注册
  fastify.post('/register', {
    schema: {
      description: 'Register a new user',
      tags: ['auth'],
      body: {
        type: 'object',
        required: ['email', 'password', 'name'],
        properties: {
          email: { type: 'string', format: 'email' },
          password: { type: 'string', minLength: 8 },
          name: { type: 'string', minLength: 1, maxLength: 100 }
        }
      },
      response: {
        201: {
          type: 'object',
          properties: {
            user: {
              type: 'object',
              properties: {
                id: { type: 'string' },
                email: { type: 'string' },
                name: { type: 'string' },
                createdAt: { type: 'string' }
              }
            },
            tokens: {
              type: 'object',
              properties: {
                accessToken: { type: 'string' },
                refreshToken: { type: 'string' }
              }
            }
          }
        }
      }
    }
  }, async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      const { email, password, name } = registerSchema.parse(request.body)

      // 检查用户是否已存在
      const existingUser = await fastify.db.getUserByEmail(email)
      if (existingUser) {
        reply.status(409).send({
          error: 'Conflict',
          message: 'User with this email already exists'
        })
        return
      }

      // 加密密码
      const hashedPassword = await bcrypt.hash(password, 12)

      // 创建用户
      const user = await fastify.db.createUser({
        email,
        password: hashedPassword,
        name,
        createdAt: new Date().toISOString()
      })

      // 生成JWT tokens
      const accessToken = fastify.jwt.sign(
        { userId: user.id, email: user.email },
        { expiresIn: '15m' }
      )

      const refreshToken = fastify.jwt.sign(
        { userId: user.id, type: 'refresh' },
        { expiresIn: config.JWT_EXPIRES_IN }
      )

      // 存储refresh token到Redis
      await fastify.redis.setex(`refresh_token:${user.id}`, 7 * 24 * 60 * 60, refreshToken)

      reply.status(201).send({
        user: {
          id: user.id,
          email: user.email,
          name: user.name,
          createdAt: user.createdAt
        },
        tokens: {
          accessToken,
          refreshToken
        }
      })
    } catch (error) {
      if (error instanceof z.ZodError) {
        reply.status(400).send({
          error: 'Validation Error',
          message: 'Invalid request body',
          details: error.errors
        })
      } else {
        reply.status(500).send({
          error: 'Internal Server Error',
          message: error instanceof Error ? error.message : 'Unknown error'
        })
      }
    }
  })

  // 用户登录
  fastify.post('/login', {
    schema: {
      description: 'User login',
      tags: ['auth'],
      body: {
        type: 'object',
        required: ['email', 'password'],
        properties: {
          email: { type: 'string', format: 'email' },
          password: { type: 'string' }
        }
      },
      response: {
        200: {
          type: 'object',
          properties: {
            user: {
              type: 'object',
              properties: {
                id: { type: 'string' },
                email: { type: 'string' },
                name: { type: 'string' }
              }
            },
            tokens: {
              type: 'object',
              properties: {
                accessToken: { type: 'string' },
                refreshToken: { type: 'string' }
              }
            }
          }
        }
      }
    }
  }, async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      const { email, password } = loginSchema.parse(request.body)

      // 查找用户
      const user = await fastify.db.getUserByEmail(email)
      if (!user) {
        reply.status(401).send({
          error: 'Unauthorized',
          message: 'Invalid email or password'
        })
        return
      }

      // 验证密码
      const isValidPassword = await bcrypt.compare(password, user.password)
      if (!isValidPassword) {
        reply.status(401).send({
          error: 'Unauthorized',
          message: 'Invalid email or password'
        })
        return
      }

      // 生成JWT tokens
      const accessToken = fastify.jwt.sign(
        { userId: user.id, email: user.email },
        { expiresIn: '15m' }
      )

      const refreshToken = fastify.jwt.sign(
        { userId: user.id, type: 'refresh' },
        { expiresIn: config.JWT_EXPIRES_IN }
      )

      // 存储refresh token到Redis
      await fastify.redis.setex(`refresh_token:${user.id}`, 7 * 24 * 60 * 60, refreshToken)

      return {
        user: {
          id: user.id,
          email: user.email,
          name: user.name
        },
        tokens: {
          accessToken,
          refreshToken
        }
      }
    } catch (error) {
      if (error instanceof z.ZodError) {
        reply.status(400).send({
          error: 'Validation Error',
          message: 'Invalid request body',
          details: error.errors
        })
      } else {
        reply.status(500).send({
          error: 'Internal Server Error',
          message: error instanceof Error ? error.message : 'Unknown error'
        })
      }
    }
  })

  // 刷新token
  fastify.post('/refresh', {
    schema: {
      description: 'Refresh access token',
      tags: ['auth'],
      body: {
        type: 'object',
        required: ['refreshToken'],
        properties: {
          refreshToken: { type: 'string' }
        }
      }
    }
  }, async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      const { refreshToken } = refreshTokenSchema.parse(request.body)

      // 验证refresh token
      const decoded = fastify.jwt.verify(refreshToken) as any
      if (decoded.type !== 'refresh') {
        reply.status(401).send({
          error: 'Unauthorized',
          message: 'Invalid refresh token'
        })
        return
      }

      // 检查Redis中的token
      const storedToken = await fastify.redis.get(`refresh_token:${decoded.userId}`)
      if (storedToken !== refreshToken) {
        reply.status(401).send({
          error: 'Unauthorized',
          message: 'Refresh token not found or expired'
        })
        return
      }

      // 获取用户信息
      const user = await fastify.db.getUserById(decoded.userId)
      if (!user) {
        reply.status(401).send({
          error: 'Unauthorized',
          message: 'User not found'
        })
        return
      }

      // 生成新的access token
      const newAccessToken = fastify.jwt.sign(
        { userId: user.id, email: user.email },
        { expiresIn: '15m' }
      )

      return {
        accessToken: newAccessToken
      }
    } catch (error) {
      if (error instanceof z.ZodError) {
        reply.status(400).send({
          error: 'Validation Error',
          message: 'Invalid request body',
          details: error.errors
        })
      } else {
        reply.status(401).send({
          error: 'Unauthorized',
          message: 'Invalid or expired refresh token'
        })
      }
    }
  })

  // 用户登出
  fastify.post('/logout', {
    preHandler: authenticate,
    schema: {
      description: 'User logout',
      tags: ['auth'],
      security: [{ Bearer: [] }]
    }
  }, async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      const user = request.user as any
      
      // 删除Redis中的refresh token
      await fastify.redis.del(`refresh_token:${user.userId}`)

      return { message: 'Logged out successfully' }
    } catch (error) {
      reply.status(500).send({
        error: 'Internal Server Error',
        message: error instanceof Error ? error.message : 'Unknown error'
      })
    }
  })

  // 获取当前用户信息
  fastify.get('/me', {
    preHandler: authenticate,
    schema: {
      description: 'Get current user information',
      tags: ['auth'],
      security: [{ Bearer: [] }],
      response: {
        200: {
          type: 'object',
          properties: {
            user: {
              type: 'object',
              properties: {
                id: { type: 'string' },
                email: { type: 'string' },
                name: { type: 'string' },
                createdAt: { type: 'string' }
              }
            }
          }
        }
      }
    }
  }, async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      const tokenUser = request.user as any
      const user = await fastify.db.getUserById(tokenUser.userId)
      
      if (!user) {
        reply.status(404).send({
          error: 'Not Found',
          message: 'User not found'
        })
        return
      }

      return {
        user: {
          id: user.id,
          email: user.email,
          name: user.name,
          createdAt: user.createdAt
        }
      }
    } catch (error) {
      reply.status(500).send({
        error: 'Internal Server Error',
        message: error instanceof Error ? error.message : 'Unknown error'
      })
    }
  })
}
