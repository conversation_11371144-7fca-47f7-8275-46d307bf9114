export async function healthRoutes(fastify) {
    // 基础健康检查
    fastify.get('/', {
        schema: {
            description: 'Basic health check',
            tags: ['health'],
            response: {
                200: {
                    type: 'object',
                    properties: {
                        status: { type: 'string' },
                        timestamp: { type: 'string' },
                        uptime: { type: 'number' }
                    }
                }
            }
        }
    }, async (request, reply) => {
        return {
            status: 'ok',
            timestamp: new Date().toISOString(),
            uptime: process.uptime()
        };
    });
    // 详细健康检查
    fastify.get('/detailed', {
        schema: {
            description: 'Detailed health check including all services',
            tags: ['health'],
            response: {
                200: {
                    type: 'object',
                    properties: {
                        status: { type: 'string' },
                        timestamp: { type: 'string' },
                        uptime: { type: 'number' },
                        services: { type: 'object' },
                        system: { type: 'object' }
                    }
                }
            }
        }
    }, async (request, reply) => {
        try {
            // 检查各个服务的健康状态
            const [dbHealth, redisHealth, exchangeHealth] = await Promise.all([
                fastify.db.healthCheck(),
                fastify.redis.healthCheck(),
                fastify.exchangeService.healthCheck()
            ]);
            // 获取市场数据服务统计
            const marketStats = await fastify.marketDataService.getMarketStats();
            // 系统信息
            const systemInfo = {
                nodeVersion: process.version,
                platform: process.platform,
                arch: process.arch,
                memory: {
                    used: Math.round(process.memoryUsage().heapUsed / 1024 / 1024),
                    total: Math.round(process.memoryUsage().heapTotal / 1024 / 1024),
                    external: Math.round(process.memoryUsage().external / 1024 / 1024)
                },
                cpu: process.cpuUsage()
            };
            // 运行中的策略统计
            const runningStrategies = fastify.strategyService.getRunningStrategies();
            const overallStatus = [dbHealth, redisHealth].every(h => h.status === 'ok') ? 'ok' : 'degraded';
            return {
                status: overallStatus,
                timestamp: new Date().toISOString(),
                uptime: process.uptime(),
                services: {
                    database: dbHealth,
                    redis: redisHealth,
                    exchanges: exchangeHealth,
                    marketData: {
                        status: 'ok',
                        stats: marketStats
                    },
                    strategies: {
                        status: 'ok',
                        running: runningStrategies.length,
                        total: runningStrategies.length // 简化统计
                    }
                },
                system: systemInfo
            };
        }
        catch (error) {
            reply.status(500).send({
                status: 'error',
                timestamp: new Date().toISOString(),
                error: error instanceof Error ? error.message : 'Unknown error'
            });
        }
    });
    // 就绪检查
    fastify.get('/ready', {
        schema: {
            description: 'Readiness check for load balancer',
            tags: ['health'],
            response: {
                200: {
                    type: 'object',
                    properties: {
                        ready: { type: 'boolean' },
                        timestamp: { type: 'string' }
                    }
                },
                503: {
                    type: 'object',
                    properties: {
                        ready: { type: 'boolean' },
                        timestamp: { type: 'string' },
                        reason: { type: 'string' }
                    }
                }
            }
        }
    }, async (request, reply) => {
        try {
            // 检查关键服务是否就绪
            const dbReady = fastify.db.isConnected();
            const redisReady = fastify.redis.isConnected();
            if (!dbReady || !redisReady) {
                reply.status(503).send({
                    ready: false,
                    timestamp: new Date().toISOString(),
                    reason: `Services not ready: ${!dbReady ? 'database ' : ''}${!redisReady ? 'redis' : ''}`.trim()
                });
                return;
            }
            return {
                ready: true,
                timestamp: new Date().toISOString()
            };
        }
        catch (error) {
            reply.status(503).send({
                ready: false,
                timestamp: new Date().toISOString(),
                reason: error instanceof Error ? error.message : 'Unknown error'
            });
        }
    });
    // 存活检查
    fastify.get('/live', {
        schema: {
            description: 'Liveness check for container orchestration',
            tags: ['health'],
            response: {
                200: {
                    type: 'object',
                    properties: {
                        alive: { type: 'boolean' },
                        timestamp: { type: 'string' }
                    }
                }
            }
        }
    }, async (request, reply) => {
        // 简单的存活检查，只要进程在运行就返回成功
        return {
            alive: true,
            timestamp: new Date().toISOString()
        };
    });
}
//# sourceMappingURL=health.js.map