import { strategyRoutes } from './strategies';
import { exchangeRoutes } from './exchanges';
import { marketDataRoutes } from './marketData';
import { authRoutes } from './auth';
import { healthRoutes } from './health';
export async function setupRoutes(fastify) {
    // 注册认证路由
    await fastify.register(authRoutes, { prefix: '/api/auth' });
    // 注册健康检查路由
    await fastify.register(healthRoutes, { prefix: '/api/health' });
    // 注册策略路由
    await fastify.register(strategyRoutes, { prefix: '/api/strategies' });
    // 注册交易所路由
    await fastify.register(exchangeRoutes, { prefix: '/api/exchanges' });
    // 注册市场数据路由
    await fastify.register(marketDataRoutes, { prefix: '/api/market-data' });
    // 根路由
    fastify.get('/', async (request, reply) => {
        return {
            name: 'SFQuant API',
            version: '1.0.0',
            description: 'Cryptocurrency Quantitative Strategy Management System API',
            timestamp: new Date().toISOString()
        };
    });
    // 404处理
    fastify.setNotFoundHandler(async (request, reply) => {
        reply.status(404).send({
            error: 'Not Found',
            message: `Route ${request.method} ${request.url} not found`,
            timestamp: new Date().toISOString()
        });
    });
}
//# sourceMappingURL=index.js.map