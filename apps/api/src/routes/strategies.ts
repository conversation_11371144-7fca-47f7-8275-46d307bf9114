import { FastifyInstance, FastifyRequest, FastifyReply } from 'fastify'
import { z } from 'zod'
import { StrategyConfig, StrategyLanguage, StrategyType } from '@sfquant/core'

// 请求验证schemas
const createStrategySchema = z.object({
  name: z.string().min(1).max(100),
  description: z.string().optional(),
  type: z.enum(['arbitrage', 'trend', 'high_frequency', 'market_maker', 'ai_ml']),
  language: z.enum(['typescript', 'python']),
  code: z.string().min(1),
  parameters: z.record(z.any()).default({}),
  exchanges: z.array(z.string()).min(1),
  symbols: z.array(z.string()).min(1),
  enabled: z.boolean().default(false),
  riskLimits: z.object({
    maxPositionSize: z.number().positive().optional(),
    maxDailyLoss: z.number().positive().optional(),
    maxDrawdown: z.number().min(0).max(1).optional()
  }).optional()
})

const updateStrategySchema = createStrategySchema.partial()

const validateStrategySchema = z.object({
  language: z.enum(['typescript', 'python']),
  code: z.string().min(1)
})

export async function strategyRoutes(fastify: FastifyInstance): Promise<void> {
  // 获取所有策略
  fastify.get('/', {
    schema: {
      description: 'Get all strategies',
      tags: ['strategies'],
      response: {
        200: {
          type: 'object',
          properties: {
            strategies: {
              type: 'array',
              items: { type: 'object' }
            }
          }
        }
      }
    }
  }, async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      const strategies = await fastify.strategyService.getAllStrategies()
      return { strategies }
    } catch (error) {
      reply.status(500).send({
        error: 'Internal Server Error',
        message: error instanceof Error ? error.message : 'Unknown error'
      })
    }
  })

  // 创建策略
  fastify.post('/', {
    schema: {
      description: 'Create a new strategy',
      tags: ['strategies'],
      body: {
        type: 'object',
        required: ['name', 'type', 'language', 'code', 'exchanges', 'symbols'],
        properties: {
          name: { type: 'string' },
          description: { type: 'string' },
          type: { type: 'string', enum: ['arbitrage', 'trend', 'high_frequency', 'market_maker', 'ai_ml'] },
          language: { type: 'string', enum: ['typescript', 'python'] },
          code: { type: 'string' },
          parameters: { type: 'object' },
          exchanges: { type: 'array', items: { type: 'string' } },
          symbols: { type: 'array', items: { type: 'string' } },
          enabled: { type: 'boolean' },
          riskLimits: { type: 'object' }
        }
      },
      response: {
        201: {
          type: 'object',
          properties: {
            strategy: { type: 'object' }
          }
        }
      }
    }
  }, async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      const body = createStrategySchema.parse(request.body)
      const strategy = await fastify.strategyService.createStrategy(body)
      
      reply.status(201).send({ strategy })
    } catch (error) {
      if (error instanceof z.ZodError) {
        reply.status(400).send({
          error: 'Validation Error',
          message: 'Invalid request body',
          details: error.errors
        })
      } else {
        reply.status(500).send({
          error: 'Internal Server Error',
          message: error instanceof Error ? error.message : 'Unknown error'
        })
      }
    }
  })

  // 获取单个策略
  fastify.get('/:id', {
    schema: {
      description: 'Get strategy by ID',
      tags: ['strategies'],
      params: {
        type: 'object',
        properties: {
          id: { type: 'string' }
        },
        required: ['id']
      },
      response: {
        200: {
          type: 'object',
          properties: {
            strategy: { type: 'object' }
          }
        },
        404: {
          type: 'object',
          properties: {
            error: { type: 'string' },
            message: { type: 'string' }
          }
        }
      }
    }
  }, async (request: FastifyRequest<{ Params: { id: string } }>, reply: FastifyReply) => {
    try {
      const { id } = request.params
      const strategy = await fastify.strategyService.getStrategy(id)
      
      if (!strategy) {
        reply.status(404).send({
          error: 'Not Found',
          message: `Strategy ${id} not found`
        })
        return
      }

      return { strategy }
    } catch (error) {
      reply.status(500).send({
        error: 'Internal Server Error',
        message: error instanceof Error ? error.message : 'Unknown error'
      })
    }
  })

  // 更新策略
  fastify.put('/:id', {
    schema: {
      description: 'Update strategy',
      tags: ['strategies'],
      params: {
        type: 'object',
        properties: {
          id: { type: 'string' }
        },
        required: ['id']
      },
      body: {
        type: 'object',
        properties: {
          name: { type: 'string' },
          description: { type: 'string' },
          type: { type: 'string', enum: ['arbitrage', 'trend', 'high_frequency', 'market_maker', 'ai_ml'] },
          language: { type: 'string', enum: ['typescript', 'python'] },
          code: { type: 'string' },
          parameters: { type: 'object' },
          exchanges: { type: 'array', items: { type: 'string' } },
          symbols: { type: 'array', items: { type: 'string' } },
          enabled: { type: 'boolean' },
          riskLimits: { type: 'object' }
        }
      }
    }
  }, async (request: FastifyRequest<{ Params: { id: string } }>, reply: FastifyReply) => {
    try {
      const { id } = request.params
      const updates = updateStrategySchema.parse(request.body)
      
      const strategy = await fastify.strategyService.updateStrategy(id, updates)
      return { strategy }
    } catch (error) {
      if (error instanceof z.ZodError) {
        reply.status(400).send({
          error: 'Validation Error',
          message: 'Invalid request body',
          details: error.errors
        })
      } else {
        reply.status(500).send({
          error: 'Internal Server Error',
          message: error instanceof Error ? error.message : 'Unknown error'
        })
      }
    }
  })

  // 删除策略
  fastify.delete('/:id', {
    schema: {
      description: 'Delete strategy',
      tags: ['strategies'],
      params: {
        type: 'object',
        properties: {
          id: { type: 'string' }
        },
        required: ['id']
      }
    }
  }, async (request: FastifyRequest<{ Params: { id: string } }>, reply: FastifyReply) => {
    try {
      const { id } = request.params
      await fastify.strategyService.deleteStrategy(id)
      
      reply.status(204).send()
    } catch (error) {
      reply.status(500).send({
        error: 'Internal Server Error',
        message: error instanceof Error ? error.message : 'Unknown error'
      })
    }
  })

  // 验证策略代码
  fastify.post('/validate', {
    schema: {
      description: 'Validate strategy code',
      tags: ['strategies'],
      body: {
        type: 'object',
        required: ['language', 'code'],
        properties: {
          language: { type: 'string', enum: ['typescript', 'python'] },
          code: { type: 'string' }
        }
      }
    }
  }, async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      const { language, code } = validateStrategySchema.parse(request.body)
      const validation = await fastify.strategyService.validateStrategy(language as StrategyLanguage, code)
      
      return { validation }
    } catch (error) {
      if (error instanceof z.ZodError) {
        reply.status(400).send({
          error: 'Validation Error',
          message: 'Invalid request body',
          details: error.errors
        })
      } else {
        reply.status(500).send({
          error: 'Internal Server Error',
          message: error instanceof Error ? error.message : 'Unknown error'
        })
      }
    }
  })

  // 启动策略
  fastify.post('/:id/start', {
    schema: {
      description: 'Start strategy execution',
      tags: ['strategies'],
      params: {
        type: 'object',
        properties: {
          id: { type: 'string' }
        },
        required: ['id']
      }
    }
  }, async (request: FastifyRequest<{ Params: { id: string } }>, reply: FastifyReply) => {
    try {
      const { id } = request.params
      await fastify.strategyService.startStrategy(id)
      
      return { message: 'Strategy started successfully' }
    } catch (error) {
      reply.status(500).send({
        error: 'Internal Server Error',
        message: error instanceof Error ? error.message : 'Unknown error'
      })
    }
  })

  // 停止策略
  fastify.post('/:id/stop', {
    schema: {
      description: 'Stop strategy execution',
      tags: ['strategies'],
      params: {
        type: 'object',
        properties: {
          id: { type: 'string' }
        },
        required: ['id']
      }
    }
  }, async (request: FastifyRequest<{ Params: { id: string } }>, reply: FastifyReply) => {
    try {
      const { id } = request.params
      await fastify.strategyService.stopStrategy(id)
      
      return { message: 'Strategy stopped successfully' }
    } catch (error) {
      reply.status(500).send({
        error: 'Internal Server Error',
        message: error instanceof Error ? error.message : 'Unknown error'
      })
    }
  })

  // 获取策略执行状态
  fastify.get('/:id/status', {
    schema: {
      description: 'Get strategy execution status',
      tags: ['strategies'],
      params: {
        type: 'object',
        properties: {
          id: { type: 'string' }
        },
        required: ['id']
      }
    }
  }, async (request: FastifyRequest<{ Params: { id: string } }>, reply: FastifyReply) => {
    try {
      const { id } = request.params
      const execution = fastify.strategyService.getStrategyExecution(id)
      const stats = await fastify.strategyService.getStrategyStats(id)
      
      return { 
        execution: execution || null,
        stats
      }
    } catch (error) {
      reply.status(500).send({
        error: 'Internal Server Error',
        message: error instanceof Error ? error.message : 'Unknown error'
      })
    }
  })

  // 获取所有运行中的策略
  fastify.get('/running/all', {
    schema: {
      description: 'Get all running strategies',
      tags: ['strategies']
    }
  }, async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      const runningStrategies = fastify.strategyService.getRunningStrategies()
      return { strategies: runningStrategies }
    } catch (error) {
      reply.status(500).send({
        error: 'Internal Server Error',
        message: error instanceof Error ? error.message : 'Unknown error'
      })
    }
  })
}
