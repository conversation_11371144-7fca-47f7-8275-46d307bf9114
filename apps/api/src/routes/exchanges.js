import { z } from 'zod';
const addExchangeSchema = z.object({
    id: z.string().min(1),
    name: z.string().min(1),
    type: z.enum(['cex', 'dex']),
    apiKey: z.string().optional(),
    secret: z.string().optional(),
    passphrase: z.string().optional(),
    sandbox: z.boolean().default(false),
    chainId: z.number().optional(),
    rpcUrl: z.string().optional(),
    privateKey: z.string().optional(),
    options: z.record(z.any()).optional()
});
export async function exchangeRoutes(fastify) {
    // 获取所有交易所
    fastify.get('/', {
        schema: {
            description: 'Get all exchanges',
            tags: ['exchanges'],
            response: {
                200: {
                    type: 'object',
                    properties: {
                        exchanges: {
                            type: 'array',
                            items: {
                                type: 'object',
                                properties: {
                                    id: { type: 'string' },
                                    name: { type: 'string' },
                                    type: { type: 'string' },
                                    initialized: { type: 'boolean' }
                                }
                            }
                        }
                    }
                }
            }
        }
    }, async (request, reply) => {
        try {
            const exchanges = fastify.exchangeService.getExchangeList();
            return { exchanges };
        }
        catch (error) {
            reply.status(500).send({
                error: 'Internal Server Error',
                message: error instanceof Error ? error.message : 'Unknown error'
            });
        }
    });
    // 添加交易所
    fastify.post('/', {
        schema: {
            description: 'Add a new exchange',
            tags: ['exchanges'],
            body: {
                type: 'object',
                required: ['id', 'name', 'type'],
                properties: {
                    id: { type: 'string' },
                    name: { type: 'string' },
                    type: { type: 'string', enum: ['cex', 'dex'] },
                    apiKey: { type: 'string' },
                    secret: { type: 'string' },
                    passphrase: { type: 'string' },
                    sandbox: { type: 'boolean' },
                    chainId: { type: 'number' },
                    rpcUrl: { type: 'string' },
                    privateKey: { type: 'string' },
                    options: { type: 'object' }
                }
            }
        }
    }, async (request, reply) => {
        try {
            const exchangeConfig = addExchangeSchema.parse(request.body);
            await fastify.exchangeService.addExchange(exchangeConfig);
            reply.status(201).send({ message: 'Exchange added successfully' });
        }
        catch (error) {
            if (error instanceof z.ZodError) {
                reply.status(400).send({
                    error: 'Validation Error',
                    message: 'Invalid request body',
                    details: error.errors
                });
            }
            else {
                reply.status(500).send({
                    error: 'Internal Server Error',
                    message: error instanceof Error ? error.message : 'Unknown error'
                });
            }
        }
    });
    // 获取交易所市场
    fastify.get('/:exchangeId/markets', {
        schema: {
            description: 'Get exchange markets',
            tags: ['exchanges'],
            params: {
                type: 'object',
                properties: {
                    exchangeId: { type: 'string' }
                },
                required: ['exchangeId']
            }
        }
    }, async (request, reply) => {
        try {
            const { exchangeId } = request.params;
            const markets = await fastify.exchangeService.getMarkets(exchangeId);
            return { markets };
        }
        catch (error) {
            reply.status(500).send({
                error: 'Internal Server Error',
                message: error instanceof Error ? error.message : 'Unknown error'
            });
        }
    });
    // 获取交易对价格
    fastify.get('/:exchangeId/ticker/:symbol', {
        schema: {
            description: 'Get ticker for a symbol',
            tags: ['exchanges'],
            params: {
                type: 'object',
                properties: {
                    exchangeId: { type: 'string' },
                    symbol: { type: 'string' }
                },
                required: ['exchangeId', 'symbol']
            }
        }
    }, async (request, reply) => {
        try {
            const { exchangeId, symbol } = request.params;
            const ticker = await fastify.exchangeService.getTicker(exchangeId, symbol);
            return { ticker };
        }
        catch (error) {
            reply.status(500).send({
                error: 'Internal Server Error',
                message: error instanceof Error ? error.message : 'Unknown error'
            });
        }
    });
    // 获取订单簿
    fastify.get('/:exchangeId/orderbook/:symbol', {
        schema: {
            description: 'Get order book for a symbol',
            tags: ['exchanges'],
            params: {
                type: 'object',
                properties: {
                    exchangeId: { type: 'string' },
                    symbol: { type: 'string' }
                },
                required: ['exchangeId', 'symbol']
            },
            querystring: {
                type: 'object',
                properties: {
                    limit: { type: 'number', minimum: 1, maximum: 1000 }
                }
            }
        }
    }, async (request, reply) => {
        try {
            const { exchangeId, symbol } = request.params;
            const { limit } = request.query;
            const orderBook = await fastify.exchangeService.getOrderBook(exchangeId, symbol, limit);
            return { orderBook };
        }
        catch (error) {
            reply.status(500).send({
                error: 'Internal Server Error',
                message: error instanceof Error ? error.message : 'Unknown error'
            });
        }
    });
    // 获取账户余额
    fastify.get('/:exchangeId/balance', {
        schema: {
            description: 'Get account balance',
            tags: ['exchanges'],
            params: {
                type: 'object',
                properties: {
                    exchangeId: { type: 'string' }
                },
                required: ['exchangeId']
            }
        }
    }, async (request, reply) => {
        try {
            const { exchangeId } = request.params;
            const balance = await fastify.exchangeService.getBalance(exchangeId);
            return { balance };
        }
        catch (error) {
            reply.status(500).send({
                error: 'Internal Server Error',
                message: error instanceof Error ? error.message : 'Unknown error'
            });
        }
    });
    // 获取多交易所价格比较
    fastify.get('/compare/:symbol', {
        schema: {
            description: 'Compare prices across exchanges',
            tags: ['exchanges'],
            params: {
                type: 'object',
                properties: {
                    symbol: { type: 'string' }
                },
                required: ['symbol']
            }
        }
    }, async (request, reply) => {
        try {
            const { symbol } = request.params;
            const prices = await fastify.exchangeService.getMultiExchangePrices(symbol);
            return { prices };
        }
        catch (error) {
            reply.status(500).send({
                error: 'Internal Server Error',
                message: error instanceof Error ? error.message : 'Unknown error'
            });
        }
    });
    // 寻找套利机会
    fastify.get('/arbitrage/:symbol', {
        schema: {
            description: 'Find arbitrage opportunities',
            tags: ['exchanges'],
            params: {
                type: 'object',
                properties: {
                    symbol: { type: 'string' }
                },
                required: ['symbol']
            }
        }
    }, async (request, reply) => {
        try {
            const { symbol } = request.params;
            const opportunities = await fastify.exchangeService.findArbitrageOpportunities(symbol);
            return { opportunities };
        }
        catch (error) {
            reply.status(500).send({
                error: 'Internal Server Error',
                message: error instanceof Error ? error.message : 'Unknown error'
            });
        }
    });
    // 交易所健康检查
    fastify.get('/health', {
        schema: {
            description: 'Check exchange health',
            tags: ['exchanges']
        }
    }, async (request, reply) => {
        try {
            const health = await fastify.exchangeService.healthCheck();
            return { health };
        }
        catch (error) {
            reply.status(500).send({
                error: 'Internal Server Error',
                message: error instanceof Error ? error.message : 'Unknown error'
            });
        }
    });
}
//# sourceMappingURL=exchanges.js.map