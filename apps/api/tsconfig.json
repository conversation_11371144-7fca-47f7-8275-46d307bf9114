{"extends": "../../tools/typescript-config/base.json", "compilerOptions": {"outDir": "dist", "rootDir": "src", "baseUrl": ".", "paths": {"@sfquant/core": ["../../packages/core/src"], "@sfquant/ccxt-adapter": ["../../packages/ccxt-adapter/src"], "@sfquant/dex-adapter": ["../../packages/dex-adapter/src"], "@sfquant/strategy-runtime": ["../../packages/strategy-runtime/src"]}}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", "**/*.test.ts", "**/*.spec.ts"]}