{"name": "sfquant", "version": "1.0.0", "description": "Cryptocurrency Quantitative Strategy Management System", "private": true, "workspaces": ["packages/*", "apps/*"], "scripts": {"dev": "turbo run dev", "build": "turbo run build", "test": "turbo run test", "lint": "turbo run lint", "clean": "turbo run clean", "type-check": "turbo run type-check"}, "devDependencies": {"@types/node": "^20.10.0", "turbo": "^1.11.0", "typescript": "^5.3.0", "prettier": "^3.1.0", "eslint": "^8.55.0", "@typescript-eslint/eslint-plugin": "^6.13.0", "@typescript-eslint/parser": "^6.13.0"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "repository": {"type": "git", "url": "https://github.com/your-username/sfquant.git"}, "keywords": ["cryptocurrency", "quantitative", "trading", "strategy", "defi", "cefi", "typescript", "python"], "author": "Your Name", "license": "MIT"}