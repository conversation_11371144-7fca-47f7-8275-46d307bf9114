{"$schema": "https://json.schemastore.org/tsconfig", "display": "<PERSON><PERSON><PERSON>", "compilerOptions": {"composite": false, "declaration": true, "declarationMap": true, "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "inlineSources": false, "isolatedModules": true, "moduleResolution": "node", "noUnusedLocals": false, "noUnusedParameters": false, "preserveWatchOutput": true, "skipLibCheck": true, "strict": true, "strictNullChecks": true, "target": "ES2020", "module": "ESNext", "lib": ["ES2020"], "allowJs": true, "checkJs": false, "incremental": true, "noEmit": false, "outDir": "dist", "rootDir": "src", "sourceMap": true, "resolveJsonModule": true}, "exclude": ["node_modules", "dist"]}