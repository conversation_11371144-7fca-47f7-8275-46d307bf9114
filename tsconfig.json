{"extends": "./tools/typescript-config/base.json", "compilerOptions": {"baseUrl": ".", "paths": {"@sfquant/core": ["./packages/core/src"], "@sfquant/ccxt-adapter": ["./packages/ccxt-adapter/src"], "@sfquant/dex-adapter": ["./packages/dex-adapter/src"], "@sfquant/strategy-runtime": ["./packages/strategy-runtime/src"], "@sfquant/ui": ["./packages/ui/src"]}}, "include": ["packages/*/src/**/*", "apps/*/src/**/*"], "exclude": ["node_modules", "dist", ".next", "coverage"]}