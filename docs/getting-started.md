# SFQuant 快速开始指南

## 项目概述

SFQuant是一个专注于虚拟货币量化策略开发的管理系统，支持多种策略类型和多语言开发。

### 支持的策略类型
- **套利策略** - 跨交易所价差套利
- **趋势策略** - 基于技术指标的趋势跟踪
- **高频策略** - 微秒级高频交易
- **做市商策略** - 双边报价和流动性提供
- **AI策略** - 机器学习驱动的智能交易

### 支持的交易所
- **CEX** - 通过CCXT支持200+中心化交易所
- **DEX** - 支持主流去中心化交易所

### 支持的编程语言
- **TypeScript** - 原生支持，完整类型安全
- **Python** - 通过运行时桥接支持

## 环境要求

- Node.js >= 18.0.0
- npm >= 8.0.0
- Python >= 3.8 (用于Python策略)
- PostgreSQL >= 14 (生产环境)
- Redis >= 6 (生产环境)

## 安装和启动

### 1. 安装依赖

```bash
npm install
```

### 2. 构建项目

```bash
npm run build
```

### 3. 启动开发环境

```bash
npm run dev
```

## 项目结构

```
sfquant/
├── apps/                    # 应用程序
│   ├── web/                # Next.js 前端应用
│   ├── api/                # API 服务
│   └── strategy-engine/    # 策略执行引擎
├── packages/               # 共享包
│   ├── core/              # 核心类型和工具
│   ├── ccxt-adapter/      # CCXT 交易所适配器
│   ├── dex-adapter/       # DEX 适配器
│   ├── strategy-runtime/  # 多语言策略运行时
│   └── ui/                # 共享UI组件
├── tools/                 # 开发工具
│   ├── eslint-config/     # ESLint 配置
│   └── typescript-config/ # TypeScript 配置
└── docs/                  # 文档
```

## 核心概念

### 1. 统一交易接口

所有交易所（CEX和DEX）都通过统一的接口进行操作：

```typescript
interface UnifiedExchange {
  fetchTicker(symbol: string): Promise<MarketData>
  fetchOrderBook(symbol: string): Promise<OrderBook>
  createOrder(order: UnifiedOrder): Promise<OrderResult>
  fetchBalance(): Promise<Record<string, Balance>>
  // ... 更多方法
}
```

### 2. 多语言策略运行时

策略可以用多种语言编写：

```typescript
interface StrategyRuntime {
  language: 'typescript' | 'python'
  execute(code: string, context: StrategyContext): Promise<StrategyResult>
  validate(code: string): Promise<ValidationResult>
}
```

### 3. 策略信号

策略通过信号与交易引擎通信：

```typescript
interface Signal {
  type: 'buy' | 'sell' | 'hold'
  symbol: string
  amount?: number
  price?: number
  confidence?: number
  metadata?: Record<string, any>
}
```

## 编写第一个策略

### TypeScript策略示例

```typescript
// 简单的移动平均策略
function onTick(marketData, orderBooks, balances, openOrders) {
  const symbol = strategy.symbols[0]
  const data = marketData[symbol]
  
  if (!data) return
  
  // 获取历史价格（这里简化处理）
  const prices = [data.close]
  const shortMA = indicators.sma(prices, 5)
  const longMA = indicators.sma(prices, 20)
  
  if (shortMA[0] > longMA[0]) {
    createSignal('buy', symbol, 0.1, data.ask, 0.8)
    utils.log(`Buy signal for ${symbol}`)
  } else if (shortMA[0] < longMA[0]) {
    createSignal('sell', symbol, 0.1, data.bid, 0.8)
    utils.log(`Sell signal for ${symbol}`)
  }
}
```

### Python策略示例

```python
import numpy as np
import pandas as pd

def on_tick(market_data, order_books, balances, open_orders):
    symbol = strategy['symbols'][0]
    data = market_data.get(symbol)
    
    if not data:
        return
    
    # 使用pandas进行数据分析
    prices = pd.Series([data['close']])
    short_ma = prices.rolling(5).mean()
    long_ma = prices.rolling(20).mean()
    
    if short_ma.iloc[-1] > long_ma.iloc[-1]:
        create_signal('buy', symbol, 0.1, data['ask'], 0.8)
        utils.log(f"Buy signal for {symbol}")
    elif short_ma.iloc[-1] < long_ma.iloc[-1]:
        create_signal('sell', symbol, 0.1, data['bid'], 0.8)
        utils.log(f"Sell signal for {symbol}")
```

## 下一步

1. **阅读API文档** - 了解详细的API接口
2. **查看策略示例** - 学习不同类型的策略实现
3. **配置交易所** - 设置你的交易所API密钥
4. **运行回测** - 在历史数据上测试你的策略
5. **部署实盘** - 将策略部署到实盘交易

## 获取帮助

- [API文档](./api.md)
- [策略开发指南](./strategy-development.md)
- [部署指南](./deployment.md)
- [常见问题](./faq.md)

## 贡献

欢迎提交Issue和Pull Request！请阅读[贡献指南](../CONTRIBUTING.md)了解更多信息。
