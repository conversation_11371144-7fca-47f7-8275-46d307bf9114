{"version": 3, "file": "prepareTransactionRequest.js", "sourceRoot": "", "sources": ["../../../actions/wallet/prepareTransactionRequest.ts"], "names": [], "mappings": ";;;AACA,0EAG6C;AAC7C,sFAGmD;AACnD,wEAI4C;AAC5C,kEAGyC;AACzC,wFAGoD;AAGpD,wDAGgC;AAChC,gDAG4B;AAO5B,2DAAoD;AAKpD,+EAAwE;AACxE,yFAAkF;AA0E3E,KAAK,UAAU,yBAAyB,CAK7C,MAA2C,EAC3C,IAA2E;IAI3E,MAAM,EAAE,OAAO,EAAE,QAAQ,GAAG,MAAM,CAAC,OAAO,EAAE,KAAK,EAAE,GAAG,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,IAAI,CAAA;IAC5E,IAAI,CAAC,QAAQ;QAAE,MAAM,IAAI,iCAAoB,EAAE,CAAA;IAC/C,MAAM,OAAO,GAAG,IAAA,8BAAY,EAAC,QAAQ,CAAC,CAAA;IAEtC,MAAM,KAAK,GAAG,MAAM,IAAA,wBAAS,EAC3B,MAAM,EACN,sBAAQ,EACR,UAAU,CACX,CAAC,EAAE,QAAQ,EAAE,QAAQ,EAAE,CAAC,CAAA;IAEzB,MAAM,OAAO,GAAG,EAAE,GAAG,IAAI,EAAE,IAAI,EAAE,OAAO,CAAC,OAAO,EAAE,CAAA;IAElD,IAAI,OAAO,KAAK,KAAK,WAAW;QAC9B,OAAO,CAAC,KAAK,GAAG,MAAM,IAAA,wBAAS,EAC7B,MAAM,EACN,4CAAmB,EACnB,qBAAqB,CACtB,CAAC;YACA,OAAO,EAAE,OAAO,CAAC,OAAO;YACxB,QAAQ,EAAE,SAAS;SACpB,CAAC,CAAA;IAEJ,IAAI,OAAO,IAAI,KAAK,WAAW,EAAE;QAC/B,IAAI;YACF,OAAO,CAAC,IAAI,GAAG,IAAA,0CAAkB,EAC/B,OAAkC,CAC5B,CAAA;SACT;QAAC,MAAM;YAEN,OAAO,CAAC,IAAI;gBACV,OAAO,KAAK,CAAC,aAAa,KAAK,QAAQ,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,QAAQ,CAAA;SACjE;KACF;IAED,IAAI,OAAO,CAAC,IAAI,KAAK,SAAS,EAAE;QAE9B,MAAM,EAAE,YAAY,EAAE,oBAAoB,EAAE,GAC1C,MAAM,IAAA,mDAA2B,EAAC,MAAM,EAAE;YACxC,KAAK;YACL,KAAK;YACL,OAAO,EAAE,OAA8C;SACxD,CAAC,CAAA;QAEJ,IACE,OAAO,IAAI,CAAC,oBAAoB,KAAK,WAAW;YAChD,IAAI,CAAC,YAAY;YACjB,IAAI,CAAC,YAAY,GAAG,oBAAoB;YAExC,MAAM,IAAI,gCAAuB,CAAC;gBAChC,oBAAoB;aACrB,CAAC,CAAA;QAEJ,OAAO,CAAC,oBAAoB,GAAG,oBAAoB,CAAA;QACnD,OAAO,CAAC,YAAY,GAAG,YAAY,CAAA;KACpC;SAAM;QAEL,IACE,OAAO,IAAI,CAAC,YAAY,KAAK,WAAW;YACxC,OAAO,IAAI,CAAC,oBAAoB,KAAK,WAAW;YAEhD,MAAM,IAAI,qCAA4B,EAAE,CAAA;QAE1C,MAAM,EAAE,QAAQ,EAAE,SAAS,EAAE,GAAG,MAAM,IAAA,mDAA2B,EAAC,MAAM,EAAE;YACxE,KAAK;YACL,KAAK;YACL,OAAO,EAAE,OAA8C;YACvD,IAAI,EAAE,QAAQ;SACf,CAAC,CAAA;QACF,OAAO,CAAC,QAAQ,GAAG,SAAS,CAAA;KAC7B;IAED,IAAI,OAAO,GAAG,KAAK,WAAW;QAC5B,OAAO,CAAC,GAAG,GAAG,MAAM,IAAA,wBAAS,EAC3B,MAAM,EACN,4BAAW,EACX,aAAa,CACd,CAAC;YACA,GAAG,OAAO;YACV,OAAO,EAAE,EAAE,OAAO,EAAE,OAAO,CAAC,OAAO,EAAE,IAAI,EAAE,UAAU,EAAE;SAC/B,CAAC,CAAA;IAE7B,IAAA,gCAAa,EAAC,OAAkC,CAAC,CAAA;IAEjD,OAAO,OAIN,CAAA;AACH,CAAC;AAlGD,8DAkGC"}