{"version": 3, "file": "watchBlocks.js", "sourceRoot": "", "sources": ["../../../actions/public/watchBlocks.ts"], "names": [], "mappings": ";;;AAMA,8DAA6D;AAC7D,2DAAoD;AACpD,uDAAgD;AAChD,iDAA8D;AAC9D,2DAA6E;AAE7E,+CAAiE;AA2FjE,SAAgB,WAAW,CAMzB,MAAkC,EAClC,EACE,QAAQ,GAAG,QAAQ,EACnB,UAAU,GAAG,KAAK,EAClB,WAAW,GAAG,KAAK,EACnB,OAAO,EACP,OAAO,EACP,mBAAmB,EAAE,oBAAoB,EACzC,IAAI,EAAE,KAAK,EACX,eAAe,GAAG,MAAM,CAAC,eAAe,GACmC;IAE7E,MAAM,aAAa,GACjB,OAAO,KAAK,KAAK,WAAW,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC,SAAS,CAAC,IAAI,KAAK,WAAW,CAAA;IAC9E,MAAM,mBAAmB,GAAG,oBAAoB,IAAI,KAAK,CAAA;IAEzD,IAAI,SAES,CAAA;IAEb,MAAM,UAAU,GAAG,GAAG,EAAE;QACtB,MAAM,UAAU,GAAG,IAAA,wBAAS,EAAC;YAC3B,aAAa;YACb,MAAM,CAAC,GAAG;YACV,UAAU;YACV,WAAW;YACX,mBAAmB;YACnB,eAAe;SAChB,CAAC,CAAA;QAEF,OAAO,IAAA,oBAAO,EAAC,UAAU,EAAE,EAAE,OAAO,EAAE,OAAO,EAAE,EAAE,CAAC,IAAI,EAAE,EAAE,CACxD,IAAA,cAAI,EACF,KAAK,IAAI,EAAE;YACT,IAAI;gBACF,MAAM,KAAK,GAAG,MAAM,IAAA,wBAAS,EAC3B,MAAM,EACN,sBAAQ,EACR,UAAU,CACX,CAAC;oBACA,QAAQ;oBACR,mBAAmB;iBACpB,CAAC,CAAA;gBACF,IAAI,KAAK,CAAC,MAAM,IAAI,SAAS,EAAE,MAAM,EAAE;oBAGrC,IAAI,KAAK,CAAC,MAAM,KAAK,SAAS,CAAC,MAAM;wBAAE,OAAM;oBAI7C,IAAI,KAAK,CAAC,MAAM,GAAG,SAAS,CAAC,MAAM,GAAG,CAAC,IAAI,UAAU,EAAE;wBACrD,KAAK,IAAI,CAAC,GAAG,SAAS,EAAE,MAAM,GAAG,EAAE,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;4BAC1D,MAAM,KAAK,GAAG,CAAC,MAAM,IAAA,wBAAS,EAC5B,MAAM,EACN,sBAAQ,EACR,UAAU,CACX,CAAC;gCACA,WAAW,EAAE,CAAC;gCACd,mBAAmB;6BACpB,CAAC,CAA+B,CAAA;4BACjC,IAAI,CAAC,OAAO,CAAC,KAAY,EAAE,SAAgB,CAAC,CAAA;4BAC5C,SAAS,GAAG,KAAK,CAAA;yBAClB;qBACF;iBACF;gBAED,IAEE,CAAC,SAAS,EAAE,MAAM;oBAElB,CAAC,QAAQ,KAAK,SAAS,IAAI,CAAC,KAAK,EAAE,MAAM,CAAC;oBAG1C,CAAC,KAAK,CAAC,MAAM,IAAI,KAAK,CAAC,MAAM,GAAG,SAAS,CAAC,MAAM,CAAC,EACjD;oBACA,IAAI,CAAC,OAAO,CAAC,KAAY,EAAE,SAAgB,CAAC,CAAA;oBAC5C,SAAS,GAAG,KAAY,CAAA;iBACzB;aACF;YAAC,OAAO,GAAG,EAAE;gBACZ,IAAI,CAAC,OAAO,EAAE,CAAC,GAAY,CAAC,CAAA;aAC7B;QACH,CAAC,EACD;YACE,WAAW;YACX,QAAQ,EAAE,eAAe;SAC1B,CACF,CACF,CAAA;IACH,CAAC,CAAA;IAED,MAAM,eAAe,GAAG,GAAG,EAAE;QAC3B,IAAI,MAAM,GAAG,IAAI,CAAA;QACjB,IAAI,WAAW,GAAG,GAAG,EAAE,CAAC,CAAC,MAAM,GAAG,KAAK,CAAC,CACvC;QAAA,CAAC,KAAK,IAAI,EAAE;YACX,IAAI;gBACF,MAAM,EAAE,WAAW,EAAE,YAAY,EAAE,GAAG,MAAM,MAAM,CAAC,SAAS,CAAC,SAAS,CAAC;oBACrE,MAAM,EAAE,CAAC,UAAU,CAAC;oBACpB,MAAM,CAAC,IAAS;wBACd,IAAI,CAAC,MAAM;4BAAE,OAAM;wBACnB,MAAM,MAAM,GACV,MAAM,CAAC,KAAK,EAAE,UAAU,EAAE,KAAK,EAAE,MAAM,IAAI,sBAAW,CAAA;wBACxD,MAAM,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA;wBACjC,OAAO,CAAC,KAAK,EAAE,SAAgB,CAAC,CAAA;wBAChC,SAAS,GAAG,KAAK,CAAA;oBACnB,CAAC;oBACD,OAAO,CAAC,KAAY;wBAClB,OAAO,EAAE,CAAC,KAAK,CAAC,CAAA;oBAClB,CAAC;iBACF,CAAC,CAAA;gBACF,WAAW,GAAG,YAAY,CAAA;gBAC1B,IAAI,CAAC,MAAM;oBAAE,WAAW,EAAE,CAAA;aAC3B;YAAC,OAAO,GAAG,EAAE;gBACZ,OAAO,EAAE,CAAC,GAAY,CAAC,CAAA;aACxB;QACH,CAAC,CAAC,EAAE,CAAA;QACJ,OAAO,WAAW,CAAA;IACpB,CAAC,CAAA;IAED,OAAO,aAAa,CAAC,CAAC,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC,eAAe,EAAE,CAAA;AACzD,CAAC;AA5HD,kCA4HC"}