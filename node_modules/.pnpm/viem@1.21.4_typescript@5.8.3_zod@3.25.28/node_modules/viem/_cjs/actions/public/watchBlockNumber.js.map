{"version": 3, "file": "watchBlockNumber.js", "sourceRoot": "", "sources": ["../../../actions/public/watchBlockNumber.ts"], "names": [], "mappings": ";;;AAKA,gEAA6D;AAC7D,2DAAoD;AACpD,uDAAgD;AAChD,iDAA8D;AAC9D,2DAAoD;AAEpD,2DAG4B;AAiE5B,SAAgB,gBAAgB,CAI9B,MAAkC,EAClC,EACE,WAAW,GAAG,KAAK,EACnB,UAAU,GAAG,KAAK,EAClB,aAAa,EACb,OAAO,EACP,IAAI,EAAE,KAAK,EACX,eAAe,GAAG,MAAM,CAAC,eAAe,GACD;IAEzC,MAAM,aAAa,GACjB,OAAO,KAAK,KAAK,WAAW,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC,SAAS,CAAC,IAAI,KAAK,WAAW,CAAA;IAE9E,IAAI,eAAqD,CAAA;IAEzD,MAAM,eAAe,GAAG,GAAG,EAAE;QAC3B,MAAM,UAAU,GAAG,IAAA,wBAAS,EAAC;YAC3B,kBAAkB;YAClB,MAAM,CAAC,GAAG;YACV,WAAW;YACX,UAAU;YACV,eAAe;SAChB,CAAC,CAAA;QAEF,OAAO,IAAA,oBAAO,EAAC,UAAU,EAAE,EAAE,aAAa,EAAE,OAAO,EAAE,EAAE,CAAC,IAAI,EAAE,EAAE,CAC9D,IAAA,cAAI,EACF,KAAK,IAAI,EAAE;YACT,IAAI;gBACF,MAAM,WAAW,GAAG,MAAM,IAAA,wBAAS,EACjC,MAAM,EACN,kCAAc,EACd,gBAAgB,CACjB,CAAC,EAAE,SAAS,EAAE,CAAC,EAAE,CAAC,CAAA;gBAEnB,IAAI,eAAe,EAAE;oBAGnB,IAAI,WAAW,KAAK,eAAe;wBAAE,OAAM;oBAI3C,IAAI,WAAW,GAAG,eAAe,GAAG,CAAC,IAAI,UAAU,EAAE;wBACnD,KAAK,IAAI,CAAC,GAAG,eAAe,GAAG,EAAE,EAAE,CAAC,GAAG,WAAW,EAAE,CAAC,EAAE,EAAE;4BACvD,IAAI,CAAC,aAAa,CAAC,CAAC,EAAE,eAAe,CAAC,CAAA;4BACtC,eAAe,GAAG,CAAC,CAAA;yBACpB;qBACF;iBACF;gBAID,IAAI,CAAC,eAAe,IAAI,WAAW,GAAG,eAAe,EAAE;oBACrD,IAAI,CAAC,aAAa,CAAC,WAAW,EAAE,eAAe,CAAC,CAAA;oBAChD,eAAe,GAAG,WAAW,CAAA;iBAC9B;aACF;YAAC,OAAO,GAAG,EAAE;gBACZ,IAAI,CAAC,OAAO,EAAE,CAAC,GAAY,CAAC,CAAA;aAC7B;QACH,CAAC,EACD;YACE,WAAW;YACX,QAAQ,EAAE,eAAe;SAC1B,CACF,CACF,CAAA;IACH,CAAC,CAAA;IAED,MAAM,oBAAoB,GAAG,GAAG,EAAE;QAChC,IAAI,MAAM,GAAG,IAAI,CAAA;QACjB,IAAI,WAAW,GAAG,GAAG,EAAE,CAAC,CAAC,MAAM,GAAG,KAAK,CAAC,CACvC;QAAA,CAAC,KAAK,IAAI,EAAE;YACX,IAAI;gBACF,MAAM,EAAE,WAAW,EAAE,YAAY,EAAE,GAAG,MAAM,MAAM,CAAC,SAAS,CAAC,SAAS,CAAC;oBACrE,MAAM,EAAE,CAAC,UAAU,CAAC;oBACpB,MAAM,CAAC,IAAS;wBACd,IAAI,CAAC,MAAM;4BAAE,OAAM;wBACnB,MAAM,WAAW,GAAG,IAAA,wBAAW,EAAC,IAAI,CAAC,MAAM,EAAE,MAAM,CAAC,CAAA;wBACpD,aAAa,CAAC,WAAW,EAAE,eAAe,CAAC,CAAA;wBAC3C,eAAe,GAAG,WAAW,CAAA;oBAC/B,CAAC;oBACD,OAAO,CAAC,KAAY;wBAClB,OAAO,EAAE,CAAC,KAAK,CAAC,CAAA;oBAClB,CAAC;iBACF,CAAC,CAAA;gBACF,WAAW,GAAG,YAAY,CAAA;gBAC1B,IAAI,CAAC,MAAM;oBAAE,WAAW,EAAE,CAAA;aAC3B;YAAC,OAAO,GAAG,EAAE;gBACZ,OAAO,EAAE,CAAC,GAAY,CAAC,CAAA;aACxB;QACH,CAAC,CAAC,EAAE,CAAA;QACJ,OAAO,WAAW,CAAA;IACpB,CAAC,CAAA;IAED,OAAO,aAAa,CAAC,CAAC,CAAC,eAAe,EAAE,CAAC,CAAC,CAAC,oBAAoB,EAAE,CAAA;AACnE,CAAC;AAlGD,4CAkGC"}