{"version": 3, "file": "estimateMaxPriorityFeePerGas.js", "sourceRoot": "", "sources": ["../../../actions/public/estimateMaxPriorityFeePerGas.ts"], "names": [], "mappings": ";;;AAEA,gDAG4B;AAO5B,gEAGwC;AACxC,2DAAoD;AAEpD,+CAAgE;AAChE,qDAAyE;AAwClE,KAAK,UAAU,4BAA4B,CAIhD,MAAgC,EAChC,IAAmE;IAEnE,OAAO,qCAAqC,CAAC,MAAM,EAAE,IAAW,CAAC,CAAA;AACnE,CAAC;AARD,oEAQC;AAEM,KAAK,UAAU,qCAAqC,CAIzD,MAAgC,EAChC,IAOC;IAED,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,GAAG,MAAM,CAAC,KAAK,EAAE,OAAO,EAAE,GAAG,IAAI,IAAI,EAAE,CAAA;IACnE,IAAI,OAAO,KAAK,EAAE,IAAI,EAAE,kBAAkB,KAAK,UAAU,EAAE;QACzD,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,MAAM,IAAA,wBAAS,EAAC,MAAM,EAAE,sBAAQ,EAAE,UAAU,CAAC,CAAC,EAAE,CAAC,CAAC,CAAA;QAC3E,OAAO,KAAK,CAAC,IAAI,CAAC,kBAAkB,CAAC;YACnC,KAAK;YACL,MAAM;YACN,OAAO;SACiB,CAAC,CAAA;KAC5B;IAED,IAAI,OAAO,KAAK,EAAE,IAAI,EAAE,kBAAkB,KAAK,WAAW;QACxD,OAAO,KAAK,EAAE,IAAI,EAAE,kBAAkB,CAAA;IAExC,IAAI;QACF,MAAM,uBAAuB,GAAG,MAAM,MAAM,CAAC,OAAO,CAAC;YACnD,MAAM,EAAE,0BAA0B;SACnC,CAAC,CAAA;QACF,OAAO,IAAA,wBAAW,EAAC,uBAAuB,CAAC,CAAA;KAC5C;IAAC,MAAM;QAIN,MAAM,CAAC,KAAK,EAAE,QAAQ,CAAC,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC;YAC1C,MAAM;gBACJ,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC;gBACzB,CAAC,CAAC,IAAA,wBAAS,EAAC,MAAM,EAAE,sBAAQ,EAAE,UAAU,CAAC,CAAC,EAAE,CAAC;YAC/C,IAAA,wBAAS,EAAC,MAAM,EAAE,4BAAW,EAAE,aAAa,CAAC,CAAC,EAAE,CAAC;SAClD,CAAC,CAAA;QAEF,IAAI,OAAO,KAAK,CAAC,aAAa,KAAK,QAAQ;YACzC,MAAM,IAAI,qCAA4B,EAAE,CAAA;QAE1C,MAAM,oBAAoB,GAAG,QAAQ,GAAG,KAAK,CAAC,aAAa,CAAA;QAE3D,IAAI,oBAAoB,GAAG,EAAE;YAAE,OAAO,EAAE,CAAA;QACxC,OAAO,oBAAoB,CAAA;KAC5B;AACH,CAAC;AAnDD,sFAmDC"}