{"version": 3, "file": "toAccount.js", "sourceRoot": "", "sources": ["../../accounts/toAccount.ts"], "names": [], "mappings": ";;;AAEA,qDAG6B;AAC7B,gEAGsC;AAwBtC,SAAgB,SAAS,CACvB,MAAsB;IAEtB,IAAI,OAAO,MAAM,KAAK,QAAQ,EAAE;QAC9B,IAAI,CAAC,IAAA,wBAAS,EAAC,MAAM,CAAC;YAAE,MAAM,IAAI,gCAAmB,CAAC,EAAE,OAAO,EAAE,MAAM,EAAE,CAAC,CAAA;QAC1E,OAAO;YACL,OAAO,EAAE,MAAM;YACf,IAAI,EAAE,UAAU;SACuB,CAAA;KAC1C;IAED,IAAI,CAAC,IAAA,wBAAS,EAAC,MAAM,CAAC,OAAO,CAAC;QAC5B,MAAM,IAAI,gCAAmB,CAAC,EAAE,OAAO,EAAE,MAAM,CAAC,OAAO,EAAE,CAAC,CAAA;IAC5D,OAAO;QACL,OAAO,EAAE,MAAM,CAAC,OAAO;QACvB,WAAW,EAAE,MAAM,CAAC,WAAW;QAC/B,eAAe,EAAE,MAAM,CAAC,eAAe;QACvC,aAAa,EAAE,MAAM,CAAC,aAAa;QACnC,MAAM,EAAE,QAAQ;QAChB,IAAI,EAAE,OAAO;KAC0B,CAAA;AAC3C,CAAC;AArBD,8BAqBC"}