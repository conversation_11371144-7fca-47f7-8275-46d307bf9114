{"version": 3, "file": "formatters.js", "sourceRoot": "", "sources": ["../../../chains/optimism/formatters.ts"], "names": [], "mappings": ";;;AAGA,gEAA6D;AAC7D,8DAA6D;AAC7D,0EAG8C;AAC9C,wFAAuF;AAU1E,QAAA,kBAAkB,GAAG;IAChC,KAAK,EAAgB,IAAA,sBAAW,EAAC;QAC/B,MAAM,CACJ,IAEC;YAID,MAAM,YAAY,GAAG,IAAI,CAAC,YAAY,EAAE,GAAG,CAAC,CAAC,WAAW,EAAE,EAAE;gBAC1D,IAAI,OAAO,WAAW,KAAK,QAAQ;oBAAE,OAAO,WAAW,CAAA;gBACvD,MAAM,SAAS,GAAG,IAAA,kCAAiB,EACjC,WAA6B,CACP,CAAA;gBACxB,IAAI,SAAS,CAAC,OAAO,KAAK,MAAM,EAAE;oBAChC,SAAS,CAAC,UAAU,GAAG,WAAW,CAAC,UAAU,CAAA;oBAC7C,SAAS,CAAC,IAAI,GAAG,WAAW,CAAC,IAAI;wBAC/B,CAAC,CAAC,IAAA,wBAAW,EAAC,WAAW,CAAC,IAAI,CAAC;wBAC/B,CAAC,CAAC,SAAS,CAAA;oBACb,SAAS,CAAC,UAAU,GAAG,WAAW,CAAC,UAAU,CAAA;oBAC7C,SAAS,CAAC,IAAI,GAAG,SAAS,CAAA;iBAC3B;gBACD,OAAO,SAAS,CAAA;YAClB,CAAC,CAAmC,CAAA;YACpC,OAAO;gBACL,YAAY;gBACZ,SAAS,EAAE,IAAI,CAAC,SAAS;aAC1B,CAAA;QACH,CAAC;KACF,CAAC;IACF,WAAW,EAAgB,IAAA,kCAAiB,EAAC;QAC3C,MAAM,CAAC,IAA4B;YACjC,MAAM,WAAW,GAAG,EAAyB,CAAA;YAC7C,IAAI,IAAI,CAAC,IAAI,KAAK,MAAM,EAAE;gBACxB,WAAW,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,CAAA;gBACxC,WAAW,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,IAAA,wBAAW,EAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,SAAS,CAAA;gBACjE,WAAW,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,CAAA;gBACxC,WAAW,CAAC,IAAI,GAAG,SAAS,CAAA;aAC7B;YACD,OAAO,WAAW,CAAA;QACpB,CAAC;KACF,CAAC;IACF,kBAAkB,EAAgB,IAAA,gDAAwB,EAAC;QACzD,MAAM,CACJ,IAA4C;YAE5C,OAAO;gBACL,UAAU,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,IAAA,wBAAW,EAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,IAAI;gBACjE,SAAS,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,IAAA,wBAAW,EAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,IAAI;gBAC9D,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,IAAA,wBAAW,EAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI;gBAClD,WAAW,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,IAAI;aAChE,CAAA;QACH,CAAC;KACF,CAAC;CACgC,CAAA"}