{"version": 3, "file": "serializers.js", "sourceRoot": "", "sources": ["../../../chains/celo/serializers.ts"], "names": [], "mappings": ";;;AAAA,wDAA6D;AAC7D,kDAAgD;AAChD,oDAA2D;AAC3D,kDAA8E;AAI9E,mEAA4D;AAC5D,0DAAsD;AACtD,sDAA+C;AAC/C,4DAAqD;AACrD,4DAAqD;AACrD,2FAAoF;AACpF,6FAGwD;AAQxD,yCAAiE;AAE1D,MAAM,wBAAwB,GAEjC,CAAC,EAAE,EAAE,SAAS,EAAE,EAAE;IACpB,IAAI,IAAA,kBAAO,EAAC,EAAE,CAAC;QAAE,OAAO,yBAAyB,CAAC,EAAE,EAAE,SAAS,CAAC,CAAA;IAChE,IAAI,IAAA,kBAAO,EAAC,EAAE,CAAC;QAAE,OAAO,yBAAyB,CAAC,EAAE,EAAE,SAAS,CAAC,CAAA;IAChE,OAAO,IAAA,8CAAoB,EAAC,EAA6B,EAAE,SAAS,CAAC,CAAA;AACvE,CAAC,CAAA;AANY,QAAA,wBAAwB,4BAMpC;AAEY,QAAA,eAAe,GAAG;IAC7B,WAAW,EAAE,gCAAwB;CACF,CAAA;AAWrC,SAAS,yBAAyB,CAChC,WAAyC,EACzC,SAAqB;IAErB,sBAAsB,CAAC,WAAW,CAAC,CAAA;IACnC,MAAM,EACJ,OAAO,EACP,GAAG,EACH,KAAK,EACL,EAAE,EACF,KAAK,EACL,YAAY,EACZ,oBAAoB,EACpB,UAAU,EACV,WAAW,EACX,mBAAmB,EACnB,UAAU,EACV,IAAI,GACL,GAAG,WAAW,CAAA;IAEf,MAAM,qBAAqB,GAAG;QAC5B,IAAA,gBAAK,EAAC,OAAO,CAAC;QACd,KAAK,CAAC,CAAC,CAAC,IAAA,gBAAK,EAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI;QAC3B,oBAAoB,CAAC,CAAC,CAAC,IAAA,gBAAK,EAAC,oBAAoB,CAAC,CAAC,CAAC,CAAC,IAAI;QACzD,YAAY,CAAC,CAAC,CAAC,IAAA,gBAAK,EAAC,YAAY,CAAC,CAAC,CAAC,CAAC,IAAI;QACzC,GAAG,CAAC,CAAC,CAAC,IAAA,gBAAK,EAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI;QACvB,WAAW,IAAI,IAAI;QACnB,mBAAmB,IAAI,IAAI;QAC3B,UAAU,CAAC,CAAC,CAAC,IAAA,gBAAK,EAAC,UAAU,CAAC,CAAC,CAAC,CAAC,IAAI;QACrC,EAAE,IAAI,IAAI;QACV,KAAK,CAAC,CAAC,CAAC,IAAA,gBAAK,EAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI;QAC3B,IAAI,IAAI,IAAI;QACZ,IAAA,4CAAmB,EAAC,UAAU,CAAC;KAChC,CAAA;IAED,IAAI,SAAS,EAAE;QACb,qBAAqB,CAAC,IAAI,CACxB,SAAS,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAA,gBAAK,EAAC,CAAC,CAAC,EACrC,IAAA,cAAI,EAAC,SAAS,CAAC,CAAC,CAAC,EACjB,IAAA,cAAI,EAAC,SAAS,CAAC,CAAC,CAAC,CAClB,CAAA;KACF;IAED,OAAO,IAAA,qBAAS,EAAC;QACf,MAAM;QACN,IAAA,gBAAK,EAAC,qBAAqB,CAAC;KAC7B,CAAwC,CAAA;AAC3C,CAAC;AAED,SAAS,yBAAyB,CAChC,WAAyC,EACzC,SAAqB;IAErB,sBAAsB,CAAC,WAAW,CAAC,CAAA;IACnC,MAAM,EACJ,OAAO,EACP,GAAG,EACH,KAAK,EACL,EAAE,EACF,KAAK,EACL,YAAY,EACZ,oBAAoB,EACpB,UAAU,EACV,WAAW,EACX,IAAI,GACL,GAAG,WAAW,CAAA;IAEf,MAAM,qBAAqB,GAAG;QAC5B,IAAA,gBAAK,EAAC,OAAO,CAAC;QACd,KAAK,CAAC,CAAC,CAAC,IAAA,gBAAK,EAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI;QAC3B,oBAAoB,CAAC,CAAC,CAAC,IAAA,gBAAK,EAAC,oBAAoB,CAAC,CAAC,CAAC,CAAC,IAAI;QACzD,YAAY,CAAC,CAAC,CAAC,IAAA,gBAAK,EAAC,YAAY,CAAC,CAAC,CAAC,CAAC,IAAI;QACzC,GAAG,CAAC,CAAC,CAAC,IAAA,gBAAK,EAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI;QACvB,EAAE,IAAI,IAAI;QACV,KAAK,CAAC,CAAC,CAAC,IAAA,gBAAK,EAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI;QAC3B,IAAI,IAAI,IAAI;QACZ,IAAA,4CAAmB,EAAC,UAAU,CAAC;QAC/B,WAAY;KACb,CAAA;IAED,IAAI,SAAS,EAAE;QACb,qBAAqB,CAAC,IAAI,CACxB,SAAS,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAA,gBAAK,EAAC,CAAC,CAAC,EACrC,IAAA,cAAI,EAAC,SAAS,CAAC,CAAC,CAAC,EACjB,IAAA,cAAI,EAAC,SAAS,CAAC,CAAC,CAAC,CAClB,CAAA;KACF;IAED,OAAO,IAAA,qBAAS,EAAC;QACf,MAAM;QACN,IAAA,gBAAK,EAAC,qBAAqB,CAAC;KAC7B,CAAwC,CAAA;AAC3C,CAAC;AAGD,MAAM,mBAAmB,GAAG,EAAE,IAAI,IAAI,GAAG,EAAE,CAAA;AAE3C,SAAgB,sBAAsB,CACpC,WAAyC;IAEzC,MAAM,EACJ,OAAO,EACP,oBAAoB,EACpB,QAAQ,EACR,YAAY,EACZ,EAAE,EACF,WAAW,EACX,UAAU,EACV,mBAAmB,GACpB,GAAG,WAAW,CAAA;IACf,IAAI,OAAO,IAAI,CAAC;QAAE,MAAM,IAAI,8BAAmB,CAAC,EAAE,OAAO,EAAE,CAAC,CAAA;IAC5D,IAAI,EAAE,IAAI,CAAC,IAAA,wBAAS,EAAC,EAAE,CAAC;QAAE,MAAM,IAAI,gCAAmB,CAAC,EAAE,OAAO,EAAE,EAAE,EAAE,CAAC,CAAA;IACxE,IAAI,QAAQ;QACV,MAAM,IAAI,mBAAS,CACjB,yDAAyD,CAC1D,CAAA;IAEH,IAAI,IAAA,oBAAS,EAAC,YAAY,CAAC,IAAI,YAAY,GAAG,mBAAmB;QAC/D,MAAM,IAAI,4BAAkB,CAAC,EAAE,YAAY,EAAE,CAAC,CAAA;IAEhD,IACE,IAAA,oBAAS,EAAC,oBAAoB,CAAC;QAC/B,IAAA,oBAAS,EAAC,YAAY,CAAC;QACvB,oBAAoB,GAAG,YAAY;QAEnC,MAAM,IAAI,6BAAmB,CAAC,EAAE,YAAY,EAAE,oBAAoB,EAAE,CAAC,CAAA;IAEvE,IACE,CAAC,IAAA,oBAAS,EAAC,UAAU,CAAC,IAAI,IAAA,kBAAO,EAAC,mBAAmB,CAAC,CAAC;QACvD,CAAC,IAAA,oBAAS,EAAC,mBAAmB,CAAC,IAAI,IAAA,kBAAO,EAAC,UAAU,CAAC,CAAC,EACvD;QACA,MAAM,IAAI,mBAAS,CACjB,mEAAmE,CACpE,CAAA;KACF;IAED,IAAI,IAAA,oBAAS,EAAC,WAAW,CAAC,IAAI,CAAC,IAAA,wBAAS,EAAC,WAAW,CAAC,EAAE;QACrD,MAAM,IAAI,mBAAS,CACjB,gEAAgE,CACjE,CAAA;KACF;IAED,IAAI,IAAA,oBAAS,EAAC,mBAAmB,CAAC,IAAI,CAAC,IAAA,wBAAS,EAAC,mBAAmB,CAAC,EAAE;QACrE,MAAM,IAAI,gCAAmB,CAAC,mBAAmB,CAAC,CAAA;KACnD;IAED,IAAI,IAAA,kBAAO,EAAC,WAAW,CAAC,IAAI,IAAA,kBAAO,EAAC,mBAAmB,CAAC,EAAE;QACxD,MAAM,IAAI,mBAAS,CACjB,yFAAyF,CAC1F,CAAA;KACF;AACH,CAAC;AAtDD,wDAsDC;AAED,SAAgB,sBAAsB,CACpC,WAAyC;IAEzC,MAAM,EACJ,OAAO,EACP,oBAAoB,EACpB,QAAQ,EACR,YAAY,EACZ,EAAE,EACF,WAAW,GACZ,GAAG,WAAW,CAAA;IAEf,IAAI,OAAO,IAAI,CAAC;QAAE,MAAM,IAAI,8BAAmB,CAAC,EAAE,OAAO,EAAE,CAAC,CAAA;IAC5D,IAAI,EAAE,IAAI,CAAC,IAAA,wBAAS,EAAC,EAAE,CAAC;QAAE,MAAM,IAAI,gCAAmB,CAAC,EAAE,OAAO,EAAE,EAAE,EAAE,CAAC,CAAA;IAExE,IAAI,QAAQ;QACV,MAAM,IAAI,mBAAS,CACjB,yDAAyD,CAC1D,CAAA;IAEH,IAAI,IAAA,oBAAS,EAAC,YAAY,CAAC,IAAI,YAAY,GAAG,mBAAmB;QAC/D,MAAM,IAAI,4BAAkB,CAAC,EAAE,YAAY,EAAE,CAAC,CAAA;IAChD,IACE,IAAA,oBAAS,EAAC,oBAAoB,CAAC;QAC/B,IAAA,oBAAS,EAAC,YAAY,CAAC;QACvB,oBAAoB,GAAG,YAAY;QAEnC,MAAM,IAAI,6BAAmB,CAAC,EAAE,YAAY,EAAE,oBAAoB,EAAE,CAAC,CAAA;IAEvE,IAAI,IAAA,oBAAS,EAAC,WAAW,CAAC,IAAI,CAAC,IAAA,wBAAS,EAAC,WAAW,CAAC,EAAE;QACrD,MAAM,IAAI,mBAAS,CACjB,gEAAgE,CACjE,CAAA;KACF;IAED,IAAI,IAAA,kBAAO,EAAC,WAAW,CAAC,EAAE;QACxB,MAAM,IAAI,mBAAS,CACjB,yDAAyD,CAC1D,CAAA;KACF;AACH,CAAC;AAxCD,wDAwCC"}