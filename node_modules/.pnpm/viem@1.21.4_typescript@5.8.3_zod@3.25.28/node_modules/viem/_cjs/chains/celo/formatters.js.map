{"version": 3, "file": "formatters.js", "sourceRoot": "", "sources": ["../../../chains/celo/formatters.ts"], "names": [], "mappings": ";;;AAGA,gEAA6D;AAC7D,4DAA2D;AAC3D,8DAA6D;AAC7D,0EAG8C;AAC9C,wFAAuF;AAQvF,yCAA6C;AAEhC,QAAA,cAAc,GAAG;IAC5B,KAAK,EAAgB,IAAA,sBAAW,EAAC;QAC/B,OAAO,EAAE,CAAC,YAAY,EAAE,UAAU,EAAE,SAAS,EAAE,OAAO,EAAE,QAAQ,CAAC;QACjE,MAAM,CACJ,IAEC;YAID,MAAM,YAAY,GAAG,IAAI,CAAC,YAAY,EAAE,GAAG,CAAC,CAAC,WAAW,EAAE,EAAE;gBAC1D,IAAI,OAAO,WAAW,KAAK,QAAQ;oBAAE,OAAO,WAAW,CAAA;gBACvD,OAAO;oBACL,GAAG,IAAA,kCAAiB,EAAC,WAA6B,CAAC;oBACnD,WAAW,EAAE,WAAW,CAAC,WAAW;oBAEpC,GAAG,CAAC,WAAW,CAAC,IAAI,KAAK,MAAM;wBAC7B,CAAC,CAAC;4BACE,UAAU,EAAE,WAAW,CAAC,UAAU;gCAChC,CAAC,CAAC,IAAA,wBAAW,EAAC,WAAW,CAAC,UAAU,CAAC;gCACrC,CAAC,CAAC,IAAI;4BACR,mBAAmB,EAAE,WAAW,CAAC,mBAAmB,IAAI,IAAI;yBAC7D;wBACH,CAAC,CAAC,EAAE,CAAC;iBACR,CAAA;YACH,CAAC,CAA+B,CAAA;YAChC,OAAO;gBACL,UAAU,EAAE,IAAI,CAAC,UAAU;gBAC3B,YAAY;aACb,CAAA;QACH,CAAC;KACF,CAAC;IACF,WAAW,EAAgB,IAAA,kCAAiB,EAAC;QAC3C,MAAM,CAAC,IAAwB;YAC7B,MAAM,WAAW,GAAG,EAAE,WAAW,EAAE,IAAI,CAAC,WAAW,EAAqB,CAAA;YAExE,IAAI,IAAI,CAAC,IAAI,KAAK,MAAM;gBAAE,WAAW,CAAC,IAAI,GAAG,OAAO,CAAA;iBAC/C;gBACH,IAAI,IAAI,CAAC,IAAI,KAAK,MAAM;oBAAE,WAAW,CAAC,IAAI,GAAG,OAAO,CAAA;gBAEpD,WAAW,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU;oBACtC,CAAC,CAAC,IAAA,wBAAW,EAAC,IAAI,CAAC,UAAU,CAAC;oBAC9B,CAAC,CAAC,IAAI,CAAA;gBACR,WAAW,CAAC,mBAAmB,GAAG,IAAI,CAAC,mBAAmB,CAAA;aAC3D;YAED,OAAO,WAAW,CAAA;QACpB,CAAC;KACF,CAAC;IAEF,kBAAkB,EAAgB,IAAA,gDAAwB,EAAC;QACzD,MAAM,CAAC,IAA4B;YACjC,MAAM,OAAO,GAAG;gBACd,WAAW,EAAE,IAAI,CAAC,WAAW;aACD,CAAA;YAE9B,IAAI,IAAA,kBAAO,EAAC,IAAI,CAAC;gBAAE,OAAO,CAAC,IAAI,GAAG,MAAM,CAAA;iBACnC;gBACH,IAAI,IAAA,kBAAO,EAAC,IAAI,CAAC;oBAAE,OAAO,CAAC,IAAI,GAAG,MAAM,CAAA;gBAExC,OAAO,CAAC,UAAU;oBAChB,OAAO,IAAI,CAAC,UAAU,KAAK,WAAW;wBACpC,CAAC,CAAC,IAAA,sBAAW,EAAC,IAAI,CAAC,UAAU,CAAC;wBAC9B,CAAC,CAAC,SAAS,CAAA;gBACf,OAAO,CAAC,mBAAmB,GAAG,IAAI,CAAC,mBAAmB,CAAA;aACvD;YAED,OAAO,OAAO,CAAA;QAChB,CAAC;KACF,CAAC;CACgC,CAAA"}