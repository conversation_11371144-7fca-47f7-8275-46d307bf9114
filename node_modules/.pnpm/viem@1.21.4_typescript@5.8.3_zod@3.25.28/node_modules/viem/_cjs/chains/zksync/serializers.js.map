{"version": 3, "file": "serializers.js", "sourceRoot": "", "sources": ["../../../chains/zksync/serializers.ts"], "names": [], "mappings": ";;;AAAA,wDAA6D;AAC7D,kDAAgD;AAChD,oDAA2D;AAG3D,mEAA4D;AAC5D,0DAAsD;AACtD,4DAAqD;AACrD,4DAAqD;AACrD,6FAGwD;AAOjD,MAAM,0BAA0B,GAEnC,CAAC,EAAE,EAAE,SAAS,EAAE,EAAE;IACpB,IAAI,QAAQ,CAAC,EAAE,CAAC;QACd,OAAO,gCAAgC,CACrC,EAAyC,CAC1C,CAAA;IACH,OAAO,IAAA,8CAAoB,EAAC,EAA6B,EAAE,SAAS,CAAC,CAAA;AACvE,CAAC,CAAA;AARY,QAAA,0BAA0B,8BAQtC;AAEY,QAAA,iBAAiB,GAAG;IAC/B,WAAW,EAAE,kCAA0B;CACJ,CAAA;AAQrC,SAAS,gCAAgC,CACvC,WAAgD;IAEhD,MAAM,EACJ,OAAO,EACP,GAAG,EACH,KAAK,EACL,EAAE,EACF,IAAI,EACJ,KAAK,EACL,YAAY,EACZ,oBAAoB,EACpB,eAAe,EACf,WAAW,EACX,SAAS,EACT,cAAc,EACd,aAAa,EACb,IAAI,GACL,GAAG,WAAW,CAAA;IAEf,uBAAuB,CAAC,WAAW,CAAC,CAAA;IAEpC,MAAM,qBAAqB,GAAG;QAC5B,KAAK,CAAC,CAAC,CAAC,IAAA,gBAAK,EAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI;QAC3B,oBAAoB,CAAC,CAAC,CAAC,IAAA,gBAAK,EAAC,oBAAoB,CAAC,CAAC,CAAC,CAAC,IAAI;QACzD,YAAY,CAAC,CAAC,CAAC,IAAA,gBAAK,EAAC,YAAY,CAAC,CAAC,CAAC,CAAC,IAAI;QACzC,GAAG,CAAC,CAAC,CAAC,IAAA,gBAAK,EAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI;QACvB,EAAE,IAAI,IAAI;QACV,KAAK,CAAC,CAAC,CAAC,IAAA,gBAAK,EAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI;QAC3B,IAAI,IAAI,IAAI;QACZ,IAAA,gBAAK,EAAC,OAAO,CAAC;QACd,IAAA,gBAAK,EAAC,EAAE,CAAC;QACT,IAAA,gBAAK,EAAC,EAAE,CAAC;QACT,IAAA,gBAAK,EAAC,OAAO,CAAC;QACd,IAAI,IAAI,IAAI;QACZ,aAAa,CAAC,CAAC,CAAC,IAAA,gBAAK,EAAC,aAAa,CAAC,CAAC,CAAC,CAAC,IAAI;QAC3C,WAAW,IAAI,EAAE;QACjB,eAAe,IAAI,IAAI;QACvB,SAAS,IAAI,cAAc,CAAC,CAAC,CAAC,CAAC,SAAS,EAAE,cAAc,CAAC,CAAC,CAAC,CAAC,EAAE;KAC/D,CAAA;IAED,OAAO,IAAA,qBAAS,EAAC;QACf,MAAM;QACN,IAAA,gBAAK,EAAC,qBAAqB,CAAC;KAC7B,CAAyC,CAAA;AAC5C,CAAC;AAKD,SAAS,QAAQ,CAAC,WAA0C;IAC1D,IACE,iBAAiB,IAAI,WAAW;QAChC,WAAW,IAAI,WAAW;QAC1B,gBAAgB,IAAI,WAAW;QAC/B,eAAe,IAAI,WAAW;QAC9B,aAAa,IAAI,WAAW;QAE5B,OAAO,IAAI,CAAA;IACb,OAAO,KAAK,CAAA;AACd,CAAC;AAED,SAAgB,uBAAuB,CACrC,WAAgD;IAEhD,MAAM,EAAE,OAAO,EAAE,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE,cAAc,EAAE,GAAG,WAAW,CAAA;IACpE,IAAI,OAAO,IAAI,CAAC;QAAE,MAAM,IAAI,8BAAmB,CAAC,EAAE,OAAO,EAAE,CAAC,CAAA;IAE5D,IAAI,EAAE,IAAI,CAAC,IAAA,wBAAS,EAAC,EAAE,CAAC;QAAE,MAAM,IAAI,gCAAmB,CAAC,EAAE,OAAO,EAAE,EAAE,EAAE,CAAC,CAAA;IACxE,IAAI,IAAI,IAAI,CAAC,IAAA,wBAAS,EAAC,IAAI,CAAC;QAAE,MAAM,IAAI,gCAAmB,CAAC,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC,CAAA;IAC9E,IAAI,SAAS,IAAI,CAAC,IAAA,wBAAS,EAAC,SAAS,CAAC;QACpC,MAAM,IAAI,gCAAmB,CAAC,EAAE,OAAO,EAAE,SAAS,EAAE,CAAC,CAAA;IAEvD,IAAI,SAAS,IAAI,CAAC,cAAc,EAAE;QAChC,MAAM,IAAI,mBAAS,CACjB,+DAA+D,CAChE,CAAA;KACF;IAED,IAAI,CAAC,SAAS,IAAI,cAAc,EAAE;QAChC,MAAM,IAAI,mBAAS,CACjB,+DAA+D,CAChE,CAAA;KACF;AACH,CAAC;AAtBD,0DAsBC"}