{"version": 3, "file": "formatters.js", "sourceRoot": "", "sources": ["../../../chains/zksync/formatters.ts"], "names": [], "mappings": ";;;AAEA,gEAA0E;AAC1E,gEAA4D;AAC5D,4DAAqD;AACrD,8DAA6D;AAC7D,0DAAyD;AACzD,0EAAyE;AACzE,wFAAuF;AACvF,wFAAuF;AAc1E,QAAA,gBAAgB,GAAG;IAC9B,KAAK,EAAgB,IAAA,sBAAW,EAAC;QAC/B,MAAM,CACJ,IAEC;YAID,MAAM,YAAY,GAAG,IAAI,CAAC,YAAY,EAAE,GAAG,CAAC,CAAC,WAAW,EAAE,EAAE;gBAC1D,IAAI,OAAO,WAAW,KAAK,QAAQ;oBAAE,OAAO,WAAW,CAAA;gBACvD,MAAM,SAAS,GAAG,wBAAgB,CAAC,WAAW,CAAC,MAAM,CACnD,WAAmC,CACf,CAAA;gBACtB,IAAI,SAAS,CAAC,OAAO,KAAK,MAAM;oBAAE,SAAS,CAAC,IAAI,GAAG,QAAQ,CAAA;qBACtD,IAAI,SAAS,CAAC,OAAO,KAAK,MAAM;oBAAE,SAAS,CAAC,IAAI,GAAG,UAAU,CAAA;gBAClE,OAAO,SAAS,CAAA;YAClB,CAAC,CAAiC,CAAA;YAClC,OAAO;gBACL,aAAa,EAAE,IAAI,CAAC,aAAa;oBAC/B,CAAC,CAAC,IAAA,wBAAW,EAAC,IAAI,CAAC,aAAa,CAAC;oBACjC,CAAC,CAAC,IAAI;gBACR,gBAAgB,EAAE,IAAI,CAAC,gBAAgB;oBACrC,CAAC,CAAC,IAAA,wBAAW,EAAC,IAAI,CAAC,gBAAgB,CAAC;oBACpC,CAAC,CAAC,IAAI;gBACR,YAAY;aACb,CAAA;QACH,CAAC;KACF,CAAC;IACF,WAAW,EAAgB,IAAA,kCAAiB,EAAC;QAC3C,MAAM,CAAC,IAA0B;YAC/B,MAAM,WAAW,GAAG,EAAuB,CAAA;YAC3C,IAAI,IAAI,CAAC,IAAI,KAAK,MAAM;gBAAE,WAAW,CAAC,IAAI,GAAG,QAAQ,CAAA;iBAChD,IAAI,IAAI,CAAC,IAAI,KAAK,MAAM;gBAAE,WAAW,CAAC,IAAI,GAAG,UAAU,CAAA;YAC5D,OAAO;gBACL,GAAG,WAAW;gBACd,aAAa,EAAE,IAAI,CAAC,aAAa;oBAC/B,CAAC,CAAC,IAAA,wBAAW,EAAC,IAAI,CAAC,aAAa,CAAC;oBACjC,CAAC,CAAC,IAAI;gBACR,cAAc,EAAE,IAAI,CAAC,cAAc;oBACjC,CAAC,CAAC,IAAA,wBAAW,EAAC,IAAI,CAAC,cAAc,CAAC;oBAClC,CAAC,CAAC,IAAI;aACY,CAAA;QACxB,CAAC;KACF,CAAC;IACF,kBAAkB,EAAgB,IAAA,gDAAwB,EAAC;QACzD,MAAM,CACJ,IAA0C;YAE1C,OAAO;gBACL,aAAa,EAAE,IAAI,CAAC,aAAa;oBAC/B,CAAC,CAAC,IAAA,wBAAW,EAAC,IAAI,CAAC,aAAa,CAAC;oBACjC,CAAC,CAAC,IAAI;gBACR,cAAc,EAAE,IAAI,CAAC,cAAc;oBACjC,CAAC,CAAC,IAAA,wBAAW,EAAC,IAAI,CAAC,cAAc,CAAC;oBAClC,CAAC,CAAC,IAAI;gBACR,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,EAAE;oBAC1B,OAAO;wBACL,GAAG,IAAA,kBAAS,EAAC,GAAG,CAAC;wBACjB,aAAa,EAAE,GAAG,CAAC,aAAa;4BAC9B,CAAC,CAAC,IAAA,wBAAW,EAAC,GAAG,CAAC,aAAa,CAAC;4BAChC,CAAC,CAAC,IAAI;wBACR,mBAAmB,EAAE,IAAA,wBAAW,EAAC,GAAG,CAAC,mBAAmB,CAAC;wBACzD,OAAO,EAAE,GAAG,CAAC,OAAO;qBACrB,CAAA;gBACH,CAAC,CAAgB;gBACjB,UAAU,EAAE,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,SAAS,EAAE,EAAE;oBAC5C,OAAO;wBACL,WAAW,EAAE,IAAA,wBAAW,EAAC,SAAS,CAAC,SAAS,CAAC;wBAC7C,SAAS,EAAE,SAAS,CAAC,SAAS;wBAC9B,aAAa,EAAE,IAAA,wBAAW,EAAC,SAAS,CAAC,aAAa,CAAC;wBACnD,gBAAgB,EAAE,IAAA,wBAAW,EAAC,SAAS,CAAC,gBAAgB,CAAC;wBACzD,OAAO,EAAE,IAAA,wBAAW,EAAC,SAAS,CAAC,OAAO,CAAC;wBACvC,SAAS,EAAE,SAAS,CAAC,SAAS;wBAC9B,MAAM,EAAE,SAAS,CAAC,MAAM;wBACxB,GAAG,EAAE,SAAS,CAAC,GAAG;wBAClB,KAAK,EAAE,SAAS,CAAC,KAAK;wBACtB,eAAe,EAAE,SAAS,CAAC,eAAe;wBAC1C,QAAQ,EAAE,IAAA,wBAAW,EAAC,SAAS,CAAC,QAAQ,CAAC;qBAC1C,CAAA;gBACH,CAAC,CAAsB;aACI,CAAA;QAC/B,CAAC;KACF,CAAC;IACF,kBAAkB,EAAgB,IAAA,gDAAwB,EAAC;QACzD,OAAO,EAAE;YACP,iBAAiB;YACjB,aAAa;YACb,eAAe;YACf,WAAW;YACX,gBAAgB;SACjB;QACD,MAAM,CAAC,IAA8B;YACnC,IACE,IAAI,CAAC,aAAa;gBAClB,CAAC,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,cAAc,CAAC;gBACvC,IAAI,CAAC,WAAW;gBAChB,IAAI,CAAC,eAAe;gBAEpB,OAAO;oBACL,UAAU,EAAE;wBACV,GAAG,CAAC,IAAI,CAAC,aAAa;4BACpB,CAAC,CAAC,EAAE,aAAa,EAAE,IAAA,gBAAK,EAAC,IAAI,CAAC,aAAa,CAAC,EAAE;4BAC9C,CAAC,CAAC,EAAE,CAAC;wBACP,GAAG,CAAC,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,cAAc;4BACvC,CAAC,CAAC;gCACE,eAAe,EAAE;oCACf,SAAS,EAAE,IAAI,CAAC,SAAS;oCACzB,cAAc,EAAE,KAAK,CAAC,IAAI,CAAC,IAAA,uBAAU,EAAC,IAAI,CAAC,cAAc,CAAC,CAAC;iCAC5D;6BACF;4BACH,CAAC,CAAC,EAAE,CAAC;wBACP,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,EAAE,WAAW,EAAE,IAAI,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;wBAC9D,GAAG,CAAC,IAAI,CAAC,eAAe;4BACtB,CAAC,CAAC,EAAE,eAAe,EAAE,IAAI,CAAC,eAAe,EAAE;4BAC3C,CAAC,CAAC,EAAE,CAAC;qBACR;oBACD,IAAI,EAAE,IAAI,CAAC,IAAI,KAAK,QAAQ,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM;iBAChB,CAAA;YAClC,OAAO,EAAiC,CAAA;QAC1C,CAAC;KACF,CAAC;CACgC,CAAA"}