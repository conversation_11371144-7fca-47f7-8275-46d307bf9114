"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.baseGoerli = void 0;
const defineChain_js_1 = require("../../utils/chain/defineChain.js");
const formatters_js_1 = require("../optimism/formatters.js");
exports.baseGoerli = (0, defineChain_js_1.defineChain)({
    id: 84531,
    network: 'base-goerli',
    name: '<PERSON> Goerli',
    nativeCurrency: { name: '<PERSON><PERSON><PERSON>', symbol: 'ETH', decimals: 18 },
    rpcUrls: {
        alchemy: {
            http: ['https://base-goerli.g.alchemy.com/v2'],
            webSocket: ['wss://base-goerli.g.alchemy.com/v2'],
        },
        default: {
            http: ['https://goerli.base.org'],
        },
        public: {
            http: ['https://goerli.base.org'],
        },
    },
    blockExplorers: {
        etherscan: {
            name: 'Basescan',
            url: 'https://goerli.basescan.org',
        },
        default: {
            name: 'Basescan',
            url: 'https://goerli.basescan.org',
        },
    },
    contracts: {
        multicall3: {
            address: '******************************************',
            blockCreated: 1376988,
        },
    },
    testnet: true,
    sourceId: 5,
}, {
    formatters: formatters_js_1.formattersOptimism,
});
//# sourceMappingURL=baseGoerli.js.map