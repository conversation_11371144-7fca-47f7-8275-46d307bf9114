"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.arbitrumGoerli = void 0;
const defineChain_js_1 = require("../../utils/chain/defineChain.js");
exports.arbitrumGoerli = (0, defineChain_js_1.defineChain)({
    id: 421613,
    name: 'Arbitrum Goerli',
    network: 'arbitrum-goerli',
    nativeCurrency: {
        name: 'Arbitrum Goerli Ether',
        symbol: 'ETH',
        decimals: 18,
    },
    rpcUrls: {
        alchemy: {
            http: ['https://arb-goerli.g.alchemy.com/v2'],
            webSocket: ['wss://arb-goerli.g.alchemy.com/v2'],
        },
        infura: {
            http: ['https://arbitrum-goerli.infura.io/v3'],
            webSocket: ['wss://arbitrum-goerli.infura.io/ws/v3'],
        },
        default: {
            http: ['https://goerli-rollup.arbitrum.io/rpc'],
        },
        public: {
            http: ['https://goerli-rollup.arbitrum.io/rpc'],
        },
    },
    blockExplorers: {
        etherscan: { name: 'Arbiscan', url: 'https://goerli.arbiscan.io' },
        default: { name: 'Arbiscan', url: 'https://goerli.arbiscan.io' },
    },
    contracts: {
        multicall3: {
            address: '******************************************',
            blockCreated: 88114,
        },
    },
    testnet: true,
});
//# sourceMappingURL=arbitrumGoerli.js.map