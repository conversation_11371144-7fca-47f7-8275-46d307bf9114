"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.wanchain = void 0;
const defineChain_js_1 = require("../../utils/chain/defineChain.js");
exports.wanchain = (0, defineChain_js_1.defineChain)({
    id: 888,
    name: 'Wanchain',
    network: 'wanchain',
    nativeCurrency: { name: 'WA<PERSON><PERSON><PERSON>', symbol: 'WAN', decimals: 18 },
    rpcUrls: {
        default: {
            http: [
                'https://gwan-ssl.wandevs.org:56891',
                'https://gwan2-ssl.wandevs.org',
            ],
        },
        public: {
            http: [
                'https://gwan-ssl.wandevs.org:56891',
                'https://gwan2-ssl.wandevs.org',
            ],
        },
    },
    blockExplorers: {
        etherscan: {
            name: 'WanS<PERSON>',
            url: 'https://wanscan.org',
        },
        default: {
            name: 'WanS<PERSON>',
            url: 'https://wanscan.org',
        },
    },
    contracts: {
        multicall3: {
            address: '******************************************',
            blockCreated: 25312390,
        },
    },
});
//# sourceMappingURL=wanchain.js.map