"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.arbitrumNova = void 0;
const defineChain_js_1 = require("../../utils/chain/defineChain.js");
exports.arbitrumNova = (0, defineChain_js_1.defineChain)({
    id: 42170,
    name: 'Arbitrum Nova',
    network: 'arbitrum-nova',
    nativeCurrency: { name: 'Ether', symbol: 'ETH', decimals: 18 },
    rpcUrls: {
        blast: {
            http: ['https://arbitrum-nova.public.blastapi.io'],
            webSocket: ['wss://arbitrum-nova.public.blastapi.io'],
        },
        default: {
            http: ['https://nova.arbitrum.io/rpc'],
        },
        public: {
            http: ['https://nova.arbitrum.io/rpc'],
        },
    },
    blockExplorers: {
        etherscan: { name: 'Arbiscan', url: 'https://nova.arbiscan.io' },
        blockScout: {
            name: 'BlockScout',
            url: 'https://nova-explorer.arbitrum.io/',
        },
        default: { name: 'Arbiscan', url: 'https://nova.arbiscan.io' },
    },
    contracts: {
        multicall3: {
            address: '******************************************',
            blockCreated: 1746963,
        },
    },
});
//# sourceMappingURL=arbitrumNova.js.map