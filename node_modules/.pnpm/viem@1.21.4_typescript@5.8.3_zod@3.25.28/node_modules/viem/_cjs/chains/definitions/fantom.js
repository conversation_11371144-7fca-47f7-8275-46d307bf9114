"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.fantom = void 0;
const defineChain_js_1 = require("../../utils/chain/defineChain.js");
exports.fantom = (0, defineChain_js_1.defineChain)({
    id: 250,
    name: 'Fan<PERSON>',
    network: 'fantom',
    nativeCurrency: {
        decimals: 18,
        name: '<PERSON><PERSON>',
        symbol: 'FTM',
    },
    rpcUrls: {
        default: { http: ['https://rpc.ankr.com/fantom'] },
        public: { http: ['https://rpc.ankr.com/fantom'] },
    },
    blockExplorers: {
        etherscan: { name: 'FTMScan', url: 'https://ftmscan.com' },
        default: { name: 'FTMScan', url: 'https://ftmscan.com' },
    },
    contracts: {
        multicall3: {
            address: '******************************************',
            blockCreated: 33001987,
        },
    },
});
//# sourceMappingURL=fantom.js.map