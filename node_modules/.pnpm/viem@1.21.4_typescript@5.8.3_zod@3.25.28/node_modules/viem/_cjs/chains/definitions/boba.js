"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.boba = void 0;
const defineChain_js_1 = require("../../utils/chain/defineChain.js");
exports.boba = (0, defineChain_js_1.defineChain)({
    id: 288,
    name: 'Boba Network',
    network: 'boba',
    nativeCurrency: {
        decimals: 18,
        name: '<PERSON><PERSON>',
        symbol: 'BOBA',
    },
    rpcUrls: {
        default: { http: ['https://mainnet.boba.network'] },
        public: { http: ['https://mainnet.boba.network'] },
    },
    blockExplorers: {
        etherscan: { name: 'BOBAScan', url: 'https://bobascan.com' },
        default: { name: 'BOBAScan', url: 'https://bobascan.com' },
    },
    contracts: {
        multicall3: {
            address: '******************************************',
            blockCreated: 446859,
        },
    },
});
//# sourceMappingURL=boba.js.map