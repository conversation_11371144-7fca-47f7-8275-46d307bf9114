"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.shimmerTestnet = void 0;
const defineChain_js_1 = require("../../utils/chain/defineChain.js");
exports.shimmerTestnet = (0, defineChain_js_1.defineChain)({
    id: 1073,
    name: 'Shimmer Testnet',
    network: 'shimmer-testnet',
    nativeCurrency: {
        decimals: 18,
        name: '<PERSON>mmer',
        symbol: 'SMR',
    },
    rpcUrls: {
        public: {
            http: ['https://json-rpc.evm.testnet.shimmer.network'],
        },
        default: {
            http: ['https://json-rpc.evm.testnet.shimmer.network'],
        },
    },
    blockExplorers: {
        default: {
            name: 'Shimmer Network Explorer',
            url: 'https://explorer.evm.testnet.shimmer.network',
        },
    },
    testnet: true,
});
//# sourceMappingURL=shimmerTestnet.js.map