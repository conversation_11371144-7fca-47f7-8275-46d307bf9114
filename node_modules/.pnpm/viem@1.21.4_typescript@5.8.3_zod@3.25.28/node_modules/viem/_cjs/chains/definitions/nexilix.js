"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.nexilix = void 0;
const defineChain_js_1 = require("../../utils/chain/defineChain.js");
exports.nexilix = (0, defineChain_js_1.defineChain)({
    id: 240,
    name: 'Nexilix Smart Chain',
    network: 'nexilix',
    nativeCurrency: {
        decimals: 18,
        name: 'Nexilix',
        symbol: 'NEXILIX',
    },
    rpcUrls: {
        default: { http: ['https://rpcurl.pos.nexilix.com'] },
        public: { http: ['https://rpcurl.pos.nexilix.com'] },
    },
    blockExplorers: {
        etherscan: { name: 'NexilixScan', url: 'https://scan.nexilix.com' },
        default: { name: 'NexilixScan', url: 'https://scan.nexilix.com' },
    },
    contracts: {
        multicall3: {
            address: '******************************************',
            blockCreated: 74448,
        },
    },
});
//# sourceMappingURL=nexilix.js.map