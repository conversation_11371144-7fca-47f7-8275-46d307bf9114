"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.avalancheFuji = void 0;
const defineChain_js_1 = require("../../utils/chain/defineChain.js");
exports.avalancheFuji = (0, defineChain_js_1.defineChain)({
    id: 43113,
    name: 'Avalanche Fuji',
    network: 'avalanche-fuji',
    nativeCurrency: {
        decimals: 18,
        name: 'Avalanche Fuji',
        symbol: 'AVAX',
    },
    rpcUrls: {
        default: { http: ['https://api.avax-test.network/ext/bc/C/rpc'] },
        public: { http: ['https://api.avax-test.network/ext/bc/C/rpc'] },
    },
    blockExplorers: {
        etherscan: { name: 'SnowTrace', url: 'https://testnet.snowtrace.io' },
        default: { name: 'SnowTrace', url: 'https://testnet.snowtrace.io' },
    },
    contracts: {
        multicall3: {
            address: '******************************************',
            blockCreated: 7096959,
        },
    },
    testnet: true,
});
//# sourceMappingURL=avalancheFuji.js.map