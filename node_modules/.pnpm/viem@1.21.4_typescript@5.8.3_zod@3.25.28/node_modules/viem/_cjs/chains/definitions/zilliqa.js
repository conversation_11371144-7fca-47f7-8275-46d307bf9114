"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.zilliqa = void 0;
const defineChain_js_1 = require("../../utils/chain/defineChain.js");
exports.zilliqa = (0, defineChain_js_1.defineChain)({
    id: 32769,
    name: 'Zilliq<PERSON>',
    network: 'zilliqa',
    nativeCurrency: { name: 'Zilliq<PERSON>', symbol: 'ZIL', decimals: 18 },
    rpcUrls: {
        default: {
            http: ['https://api.zilliqa.com'],
        },
        public: {
            http: ['https://api.zilliqa.com'],
        },
    },
    blockExplorers: {
        default: {
            name: 'Ether<PERSON>',
            url: 'https://evmx.zilliqa.com',
        },
    },
    testnet: false,
});
//# sourceMappingURL=zilliqa.js.map