"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.eosTestnet = void 0;
const defineChain_js_1 = require("../../utils/chain/defineChain.js");
exports.eosTestnet = (0, defineChain_js_1.defineChain)({
    id: 15557,
    name: 'EOS EVM Testnet',
    network: 'eos',
    nativeCurrency: {
        decimals: 18,
        name: 'EOS',
        symbol: 'EOS',
    },
    rpcUrls: {
        default: { http: ['https://api.testnet.evm.eosnetwork.com'] },
        public: { http: ['https://api.testnet.evm.eosnetwork.com'] },
    },
    blockExplorers: {
        etherscan: {
            name: 'EOS EVM Testnet Explorer',
            url: 'https://explorer.testnet.evm.eosnetwork.com',
        },
        default: {
            name: 'EOS EVM Testnet Explorer',
            url: 'https://explorer.testnet.evm.eosnetwork.com',
        },
    },
    contracts: {
        multicall3: {
            address: '******************************************',
            blockCreated: 9067940,
        },
    },
    testnet: true,
});
//# sourceMappingURL=eosTestnet.js.map