"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.coreDao = void 0;
const defineChain_js_1 = require("../../utils/chain/defineChain.js");
exports.coreDao = (0, defineChain_js_1.defineChain)({
    id: 1116,
    name: 'Core Dao',
    network: 'coreDao',
    nativeCurrency: {
        decimals: 18,
        name: 'Core',
        symbol: 'CORE',
    },
    rpcUrls: {
        public: { http: ['https://rpc.coredao.org'] },
        default: { http: ['https://rpc.coredao.org'] },
    },
    blockExplorers: {
        default: { name: 'Core<PERSON><PERSON>', url: 'https://scan.coredao.org' },
        etherscan: { name: 'CoreDao', url: 'https://scan.coredao.org' },
    },
    contracts: {
        multicall3: {
            address: '******************************************',
            blockCreated: 11907934,
        },
    },
    testnet: false,
});
//# sourceMappingURL=coreDao.js.map