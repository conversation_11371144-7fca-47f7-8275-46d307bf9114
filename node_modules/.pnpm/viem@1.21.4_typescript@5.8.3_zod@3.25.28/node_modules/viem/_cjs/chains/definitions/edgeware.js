"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.edgeware = void 0;
const defineChain_js_1 = require("../../utils/chain/defineChain.js");
exports.edgeware = (0, defineChain_js_1.defineChain)({
    id: 2021,
    name: 'Edgeware EdgeEVM Mainnet',
    network: 'edgeware',
    nativeCurrency: {
        decimals: 18,
        name: 'Edgeware',
        symbol: 'EDG',
    },
    rpcUrls: {
        default: { http: ['https://edgeware-evm.jelliedowl.net'] },
        public: { http: ['https://edgeware-evm.jelliedowl.net'] },
    },
    blockExplorers: {
        etherscan: { name: 'Edgscan by Bharathcoorg', url: 'https://edgscan.live' },
        default: { name: 'Edgscan by Bharathcoorg', url: 'https://edgscan.live' },
    },
    contracts: {
        multicall3: {
            address: '******************************************',
            blockCreated: 18117872,
        },
    },
});
//# sourceMappingURL=edgeware.js.map