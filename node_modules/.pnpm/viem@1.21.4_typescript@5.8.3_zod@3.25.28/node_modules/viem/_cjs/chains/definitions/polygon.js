"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.polygon = void 0;
const defineChain_js_1 = require("../../utils/chain/defineChain.js");
exports.polygon = (0, defineChain_js_1.defineChain)({
    id: 137,
    name: 'Polygon',
    network: 'matic',
    nativeCurrency: { name: 'MATI<PERSON>', symbol: 'MATIC', decimals: 18 },
    rpcUrls: {
        alchemy: {
            http: ['https://polygon-mainnet.g.alchemy.com/v2'],
            webSocket: ['wss://polygon-mainnet.g.alchemy.com/v2'],
        },
        infura: {
            http: ['https://polygon-mainnet.infura.io/v3'],
            webSocket: ['wss://polygon-mainnet.infura.io/ws/v3'],
        },
        default: {
            http: ['https://polygon-rpc.com'],
        },
        public: {
            http: ['https://polygon-rpc.com'],
        },
    },
    blockExplorers: {
        etherscan: {
            name: 'PolygonScan',
            url: 'https://polygonscan.com',
        },
        default: {
            name: 'PolygonScan',
            url: 'https://polygonscan.com',
        },
    },
    contracts: {
        multicall3: {
            address: '******************************************',
            blockCreated: 25770160,
        },
    },
});
//# sourceMappingURL=polygon.js.map