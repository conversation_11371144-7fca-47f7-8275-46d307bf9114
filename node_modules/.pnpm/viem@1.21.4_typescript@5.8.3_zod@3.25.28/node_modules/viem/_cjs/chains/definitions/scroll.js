"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.scroll = void 0;
const defineChain_js_1 = require("../../utils/chain/defineChain.js");
exports.scroll = (0, defineChain_js_1.defineChain)({
    id: 534352,
    name: '<PERSON><PERSON>',
    network: 'scroll',
    nativeCurrency: { name: '<PERSON><PERSON>', symbol: 'ETH', decimals: 18 },
    rpcUrls: {
        default: {
            http: ['https://rpc.scroll.io'],
            webSocket: ['wss://wss-rpc.scroll.io/ws'],
        },
        public: {
            http: ['https://rpc.scroll.io'],
            webSocket: ['wss://wss-rpc.scroll.io/ws'],
        },
    },
    blockExplorers: {
        default: {
            name: 'Scrollscan',
            url: 'https://scrollscan.com',
        },
        blockscout: {
            name: 'Blockscout',
            url: 'https://blockscout.scroll.io',
        },
    },
    contracts: {
        multicall3: {
            address: '******************************************',
            blockCreated: 14,
        },
    },
    testnet: false,
});
//# sourceMappingURL=scroll.js.map