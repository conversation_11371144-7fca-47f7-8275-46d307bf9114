"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.zora = void 0;
const defineChain_js_1 = require("../../utils/chain/defineChain.js");
const formatters_js_1 = require("../optimism/formatters.js");
exports.zora = (0, defineChain_js_1.defineChain)({
    id: 7777777,
    name: 'Zora',
    network: 'zora',
    nativeCurrency: {
        decimals: 18,
        name: '<PERSON><PERSON>',
        symbol: 'ETH',
    },
    rpcUrls: {
        default: {
            http: ['https://rpc.zora.energy'],
            webSocket: ['wss://rpc.zora.energy'],
        },
        public: {
            http: ['https://rpc.zora.energy'],
            webSocket: ['wss://rpc.zora.energy'],
        },
    },
    blockExplorers: {
        default: { name: 'Explorer', url: 'https://explorer.zora.energy' },
    },
    contracts: {
        multicall3: {
            address: '******************************************',
            blockCreated: 5882,
        },
    },
}, {
    formatters: formatters_js_1.formattersOptimism,
});
//# sourceMappingURL=zora.js.map