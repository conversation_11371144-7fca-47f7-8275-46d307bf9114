"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.mantaTestnet = void 0;
const defineChain_js_1 = require("../../utils/chain/defineChain.js");
exports.mantaTestnet = (0, defineChain_js_1.defineChain)({
    id: 3441005,
    name: 'Manta Pacific Testnet',
    network: 'manta-testnet',
    nativeCurrency: {
        decimals: 18,
        name: 'ETH',
        symbol: 'ETH',
    },
    rpcUrls: {
        default: { http: ['https://manta-testnet.calderachain.xyz/http'] },
        public: { http: ['https://manta-testnet.calderachain.xyz/http'] },
    },
    blockExplorers: {
        etherscan: {
            name: 'Manta Testnet Explorer',
            url: 'https://pacific-explorer.testnet.manta.network',
        },
        default: {
            name: 'Manta Testnet Explorer',
            url: 'https://pacific-explorer.testnet.manta.network',
        },
    },
    contracts: {
        multicall3: {
            address: '******************************************',
            blockCreated: 419915,
        },
    },
    testnet: true,
});
//# sourceMappingURL=mantaTestnet.js.map