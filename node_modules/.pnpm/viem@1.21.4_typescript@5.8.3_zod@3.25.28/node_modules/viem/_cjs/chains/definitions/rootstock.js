"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.rootstock = void 0;
const defineChain_js_1 = require("../../utils/chain/defineChain.js");
exports.rootstock = (0, defineChain_js_1.defineChain)({
    id: 30,
    name: 'Rootstock Mainnet',
    network: 'rootstock',
    nativeCurrency: {
        decimals: 18,
        name: 'Rootstock Bitcoin',
        symbol: 'RBTC',
    },
    rpcUrls: {
        public: { http: ['https://public-node.rsk.co'] },
        default: { http: ['https://public-node.rsk.co'] },
    },
    blockExplorers: {
        blockscout: { name: 'Blockscout', url: 'https://rootstock.blockscout.com' },
        default: { name: 'RSK Explorer', url: 'https://explorer.rsk.co' },
    },
    contracts: {
        multicall3: {
            address: '******************************************',
            blockCreated: 4249540,
        },
    },
});
//# sourceMappingURL=rootstock.js.map