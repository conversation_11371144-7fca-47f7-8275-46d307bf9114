"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.bscTestnet = void 0;
const defineChain_js_1 = require("../../utils/chain/defineChain.js");
exports.bscTestnet = (0, defineChain_js_1.defineChain)({
    id: 97,
    name: 'Binance Smart Chain Testnet',
    network: 'bsc-testnet',
    nativeCurrency: {
        decimals: 18,
        name: 'BN<PERSON>',
        symbol: 'tBNB',
    },
    rpcUrls: {
        default: { http: ['https://data-seed-prebsc-1-s1.bnbchain.org:8545'] },
        public: { http: ['https://data-seed-prebsc-1-s1.bnbchain.org:8545'] },
    },
    blockExplorers: {
        etherscan: { name: 'BscScan', url: 'https://testnet.bscscan.com' },
        default: { name: 'BscScan', url: 'https://testnet.bscscan.com' },
    },
    contracts: {
        multicall3: {
            address: '******************************************',
            blockCreated: 17422483,
        },
    },
    testnet: true,
});
//# sourceMappingURL=bscTestnet.js.map