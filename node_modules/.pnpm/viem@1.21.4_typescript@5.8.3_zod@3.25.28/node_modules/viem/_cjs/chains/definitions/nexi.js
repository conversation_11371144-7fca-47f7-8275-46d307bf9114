"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.nexi = void 0;
const defineChain_js_1 = require("../../utils/chain/defineChain.js");
exports.nexi = (0, defineChain_js_1.defineChain)({
    id: 4242,
    name: 'Nexi',
    network: 'nexi',
    nativeCurrency: { name: 'Nexi', symbol: 'NEXI', decimals: 18 },
    rpcUrls: {
        default: {
            http: ['https://rpc.chain.nexi.technology'],
        },
        public: {
            http: ['https://rpc.chain.nexi.technology'],
        },
    },
    blockExplorers: {
        etherscan: {
            name: 'NexiScan',
            url: 'https://www.nexiscan.com',
        },
        default: {
            name: 'NexiScan',
            url: 'https://www.nexiscan.com',
        },
    },
    contracts: {
        multicall3: {
            address: '******************************************',
            blockCreated: 25770160,
        },
    },
});
//# sourceMappingURL=nexi.js.map