"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.bahamut = void 0;
const defineChain_js_1 = require("../../utils/chain/defineChain.js");
exports.bahamut = (0, defineChain_js_1.defineChain)({
    id: 5165,
    network: 'bahamut',
    name: '<PERSON>ham<PERSON>',
    nativeCurrency: { name: 'Fast<PERSON><PERSON>', symbol: 'FTN', decimals: 18 },
    rpcUrls: {
        default: {
            http: [
                'https://rpc1.bahamut.io',
                'https://bahamut.publicnode.com',
                'https://rpc2.bahamut.io',
            ],
            webSocket: [
                'wss://ws1.sahara.bahamutchain.com',
                'wss://bahamut.publicnode.com',
                'wss://ws2.sahara.bahamutchain.com',
            ],
        },
        public: {
            http: [
                'https://rpc1.bahamut.io',
                'https://bahamut.publicnode.com',
                'https://rpc2.bahamut.io',
            ],
            webSocket: [
                'wss://ws1.sahara.bahamutchain.com',
                'wss://bahamut.publicnode.com',
                'wss://ws2.sahara.bahamutchain.com',
            ],
        },
    },
    blockExplorers: {
        default: {
            name: 'Ftnscan',
            url: 'https://www.ftnscan.com',
        },
    },
});
//# sourceMappingURL=bahamut.js.map