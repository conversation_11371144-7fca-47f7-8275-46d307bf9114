"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.zkSyncTestnet = void 0;
const defineChain_js_1 = require("../../utils/chain/defineChain.js");
const formatters_js_1 = require("../zksync/formatters.js");
const serializers_js_1 = require("../zksync/serializers.js");
exports.zkSyncTestnet = (0, defineChain_js_1.defineChain)({
    id: 280,
    name: 'zkSync Era Testnet',
    network: 'zksync-era-testnet',
    nativeCurrency: { name: 'Ether', symbol: 'ETH', decimals: 18 },
    rpcUrls: {
        default: {
            http: ['https://testnet.era.zksync.dev'],
            webSocket: ['wss://testnet.era.zksync.dev/ws'],
        },
        public: {
            http: ['https://testnet.era.zksync.dev'],
            webSocket: ['wss://testnet.era.zksync.dev/ws'],
        },
    },
    blockExplorers: {
        default: {
            name: 'zkExplorer',
            url: 'https://goerli.explorer.zksync.io',
        },
    },
    contracts: {
        multicall3: {
            address: '******************************************',
        },
    },
    testnet: true,
}, {
    serializers: serializers_js_1.serializersZkSync,
    formatters: formatters_js_1.formattersZkSync,
});
//# sourceMappingURL=zkSyncTestnet.js.map