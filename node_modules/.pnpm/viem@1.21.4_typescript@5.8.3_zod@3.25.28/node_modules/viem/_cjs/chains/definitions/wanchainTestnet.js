"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.wanchainTestnet = void 0;
const defineChain_js_1 = require("../../utils/chain/defineChain.js");
exports.wanchainTestnet = (0, defineChain_js_1.defineChain)({
    id: 999,
    name: 'Wanchain Testnet',
    network: 'wanchainTestnet',
    nativeCurrency: { name: 'WANCHAIN', symbol: 'WANt', decimals: 18 },
    rpcUrls: {
        default: {
            http: ['https://gwan-ssl.wandevs.org:46891'],
        },
        public: {
            http: ['https://gwan-ssl.wandevs.org:46891'],
        },
    },
    blockExplorers: {
        etherscan: {
            name: 'WanScanTest',
            url: 'https://wanscan.org',
        },
        default: {
            name: 'WanScanTest',
            url: 'https://wanscan.org',
        },
    },
    contracts: {
        multicall3: {
            address: '******************************************',
            blockCreated: 24743448,
        },
    },
    testnet: true,
});
//# sourceMappingURL=wanchainTestnet.js.map