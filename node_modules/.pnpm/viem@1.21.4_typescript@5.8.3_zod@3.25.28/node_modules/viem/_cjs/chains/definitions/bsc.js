"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.bsc = void 0;
const defineChain_js_1 = require("../../utils/chain/defineChain.js");
exports.bsc = (0, defineChain_js_1.defineChain)({
    id: 56,
    name: 'BNB Smart Chain',
    network: 'bsc',
    nativeCurrency: {
        decimals: 18,
        name: 'BN<PERSON>',
        symbol: 'BNB',
    },
    rpcUrls: {
        default: { http: ['https://rpc.ankr.com/bsc'] },
        public: { http: ['https://rpc.ankr.com/bsc'] },
    },
    blockExplorers: {
        etherscan: { name: 'BscScan', url: 'https://bscscan.com' },
        default: { name: 'BscScan', url: 'https://bscscan.com' },
    },
    contracts: {
        multicall3: {
            address: '******************************************',
            blockCreated: 15921452,
        },
    },
});
//# sourceMappingURL=bsc.js.map