"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.moonbeam = void 0;
const defineChain_js_1 = require("../../utils/chain/defineChain.js");
exports.moonbeam = (0, defineChain_js_1.defineChain)({
    id: 1284,
    name: 'Moonbeam',
    network: 'moonbeam',
    nativeCurrency: {
        decimals: 18,
        name: '<PERSON><PERSON><PERSON>',
        symbol: 'GLM<PERSON>',
    },
    rpcUrls: {
        public: {
            http: ['https://moonbeam.public.blastapi.io'],
            webSocket: ['wss://moonbeam.public.blastapi.io'],
        },
        default: {
            http: ['https://moonbeam.public.blastapi.io'],
            webSocket: ['wss://moonbeam.public.blastapi.io'],
        },
    },
    blockExplorers: {
        default: {
            name: 'Moonscan',
            url: 'https://moonscan.io',
        },
        etherscan: {
            name: 'Moonscan',
            url: 'https://moonscan.io',
        },
    },
    contracts: {
        multicall3: {
            address: '******************************************',
            blockCreated: 609002,
        },
    },
    testnet: false,
});
//# sourceMappingURL=moonbeam.js.map