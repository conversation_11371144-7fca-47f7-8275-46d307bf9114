"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.shibarium = void 0;
const defineChain_js_1 = require("../../utils/chain/defineChain.js");
exports.shibarium = (0, defineChain_js_1.defineChain)({
    id: 109,
    name: 'Shibarium',
    network: 'shibarium',
    nativeCurrency: { name: 'Bone', symbol: 'BONE', decimals: 18 },
    rpcUrls: {
        default: {
            http: ['https://rpc.shibrpc.com'],
        },
        public: {
            http: ['https://rpc.shibrpc.com'],
        },
    },
    blockExplorers: {
        etherscan: {
            name: 'Blockscout',
            url: 'https://shibariumscan.io',
        },
        default: {
            name: 'Blockscout',
            url: 'https://shibariumscan.io',
        },
    },
    contracts: {
        multicall3: {
            address: '******************************************',
            blockCreated: 265900,
        },
    },
});
//# sourceMappingURL=shibarium.js.map