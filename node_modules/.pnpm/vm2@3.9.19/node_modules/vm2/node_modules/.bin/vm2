#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/Users/<USER>/CursorSpace/SFQuant/node_modules/.pnpm/vm2@3.9.19/node_modules/vm2/bin/node_modules:/Users/<USER>/CursorSpace/SFQuant/node_modules/.pnpm/vm2@3.9.19/node_modules/vm2/node_modules:/Users/<USER>/CursorSpace/SFQuant/node_modules/.pnpm/vm2@3.9.19/node_modules:/Users/<USER>/CursorSpace/SFQuant/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/Users/<USER>/CursorSpace/SFQuant/node_modules/.pnpm/vm2@3.9.19/node_modules/vm2/bin/node_modules:/Users/<USER>/CursorSpace/SFQuant/node_modules/.pnpm/vm2@3.9.19/node_modules/vm2/node_modules:/Users/<USER>/CursorSpace/SFQuant/node_modules/.pnpm/vm2@3.9.19/node_modules:/Users/<USER>/CursorSpace/SFQuant/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../bin/vm2" "$@"
else
  exec node  "$basedir/../../bin/vm2" "$@"
fi
