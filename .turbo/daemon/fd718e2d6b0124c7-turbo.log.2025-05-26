2025-05-26T01:36:58.479136Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/src/types/strategy.d.ts"), AnchoredSystemPathBuf("packages/core/src/types/strategy.js.map"), AnchoredSystemPathBuf("packages/core/src/types/strategy.js"), AnchoredSystemPathBuf("packages/core/src/types/strategy.d.ts.map")}
2025-05-26T01:36:58.479144Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@sfquant/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-05-26T01:36:58.578763Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/src/types/exchange.d.ts"), AnchoredSystemPathBuf("packages/core/src/types/exchange.js"), AnchoredSystemPathBuf("packages/ccxt-adapter/src/CCXTAdapter.d.ts"), AnchoredSystemPathBuf("packages/dex-adapter/src/protocols/UniswapV2Protocol.js"), AnchoredSystemPathBuf("packages/core/src/index.d.ts"), AnchoredSystemPathBuf("packages/dex-adapter/src/protocols/UniswapV2Protocol.js.map"), AnchoredSystemPathBuf("packages/dex-adapter/src/protocols/UniswapV3Protocol.d.ts"), AnchoredSystemPathBuf("packages/dex-adapter/src/protocols/UniswapV3Protocol.js.map"), AnchoredSystemPathBuf("packages/core/src/index.d.ts.map"), AnchoredSystemPathBuf("packages/dex-adapter/src/protocols/UniswapV3Protocol.js"), AnchoredSystemPathBuf("packages/ccxt-adapter/src/CCXTAdapter.js.map"), AnchoredSystemPathBuf("packages/dex-adapter/src/types/dex.d.ts"), AnchoredSystemPathBuf("packages/dex-adapter/src/types/dex.js"), AnchoredSystemPathBuf("packages/ccxt-adapter/src/CCXTAdapter.d.ts.map"), AnchoredSystemPathBuf("packages/core/src/types/exchange.js.map"), AnchoredSystemPathBuf("packages/core/src/index.js.map"), AnchoredSystemPathBuf("packages/ccxt-adapter/src/CCXTAdapter.js"), AnchoredSystemPathBuf("packages/dex-adapter/src/protocols/UniswapV3Protocol.d.ts.map"), AnchoredSystemPathBuf("packages/core/src/index.js"), AnchoredSystemPathBuf("packages/core/src/types/exchange.d.ts.map"), AnchoredSystemPathBuf("packages/dex-adapter/src/protocols/UniswapV2Protocol.d.ts"), AnchoredSystemPathBuf("packages/dex-adapter/src/protocols/UniswapV2Protocol.d.ts.map"), AnchoredSystemPathBuf("packages/dex-adapter/src/types/dex.d.ts.map"), AnchoredSystemPathBuf("packages/dex-adapter/src/types/dex.js.map")}
2025-05-26T01:36:58.578772Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@sfquant/ccxt-adapter"), path: AnchoredSystemPathBuf("packages/ccxt-adapter") }, WorkspacePackage { name: Other("@sfquant/dex-adapter"), path: AnchoredSystemPathBuf("packages/dex-adapter") }, WorkspacePackage { name: Other("@sfquant/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-05-26T01:36:58.679271Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/src/config/index.d.ts.map"), AnchoredSystemPathBuf("packages/strategy-runtime/src/StrategyRuntime.d.ts.map"), AnchoredSystemPathBuf("packages/strategy-runtime/src/TypeScriptRuntime.d.ts"), AnchoredSystemPathBuf("apps/api/src/config/index.js"), AnchoredSystemPathBuf("apps/api/src/config/index.js.map"), AnchoredSystemPathBuf("packages/dex-adapter/src/DEXAdapter.d.ts.map"), AnchoredSystemPathBuf("packages/dex-adapter/src/index.js"), AnchoredSystemPathBuf("packages/dex-adapter/src/index.js.map"), AnchoredSystemPathBuf("packages/dex-adapter/src/DEXAdapter.d.ts"), AnchoredSystemPathBuf("packages/dex-adapter/src/DEXAdapter.js"), AnchoredSystemPathBuf("packages/strategy-runtime/src/StrategyRuntime.js.map"), AnchoredSystemPathBuf("packages/strategy-runtime/src/TypeScriptRuntime.d.ts.map"), AnchoredSystemPathBuf("packages/strategy-runtime/src/StrategyRuntime.d.ts"), AnchoredSystemPathBuf("packages/strategy-runtime/src/TypeScriptRuntime.js"), AnchoredSystemPathBuf("packages/dex-adapter/src/index.d.ts"), AnchoredSystemPathBuf("packages/strategy-runtime/src/StrategyRuntime.js"), AnchoredSystemPathBuf("packages/dex-adapter/src/index.d.ts.map"), AnchoredSystemPathBuf("apps/api/src/config/index.d.ts"), AnchoredSystemPathBuf("packages/strategy-runtime/src/TypeScriptRuntime.js.map"), AnchoredSystemPathBuf("packages/dex-adapter/src/DEXAdapter.js.map")}
2025-05-26T01:36:58.679280Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@sfquant/api"), path: AnchoredSystemPathBuf("apps/api") }, WorkspacePackage { name: Other("@sfquant/dex-adapter"), path: AnchoredSystemPathBuf("packages/dex-adapter") }, WorkspacePackage { name: Other("@sfquant/strategy-runtime"), path: AnchoredSystemPathBuf("packages/strategy-runtime") }}))
2025-05-26T01:36:58.679302Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-05-26T01:36:58.778120Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/src/routes/exchanges.js"), AnchoredSystemPathBuf("apps/api/src/routes/auth.js.map"), AnchoredSystemPathBuf("apps/api/src/routes/exchanges.js.map"), AnchoredSystemPathBuf("apps/api/src/routes/health.d.ts.map"), AnchoredSystemPathBuf("apps/api/src/routes/marketData.d.ts"), AnchoredSystemPathBuf("apps/api/src/routes/auth.d.ts.map"), AnchoredSystemPathBuf("apps/api/src/routes/auth.js"), AnchoredSystemPathBuf("apps/api/src/routes/marketData.d.ts.map"), AnchoredSystemPathBuf("apps/api/src/routes/health.d.ts"), AnchoredSystemPathBuf("apps/api/src/routes/marketData.js"), AnchoredSystemPathBuf("apps/api/src/routes/exchanges.d.ts.map"), AnchoredSystemPathBuf("apps/api/src/routes/exchanges.d.ts"), AnchoredSystemPathBuf("apps/api/src/routes/auth.d.ts"), AnchoredSystemPathBuf("apps/api/src/routes/health.js.map"), AnchoredSystemPathBuf("apps/api/src/routes/marketData.js.map"), AnchoredSystemPathBuf("apps/api/src/routes/strategies.js"), AnchoredSystemPathBuf("apps/api/src/routes/strategies.js.map"), AnchoredSystemPathBuf("apps/api/src/routes/health.js"), AnchoredSystemPathBuf("apps/api/src/routes/strategies.d.ts"), AnchoredSystemPathBuf("apps/api/src/routes/strategies.d.ts.map")}
2025-05-26T01:36:58.778128Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@sfquant/api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-05-26T01:36:58.879256Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/src/routes/index.js.map"), AnchoredSystemPathBuf("apps/api/src/routes/index.d.ts.map"), AnchoredSystemPathBuf("apps/api/src/routes/index.d.ts"), AnchoredSystemPathBuf("apps/api/src/services/DatabaseService.d.ts.map"), AnchoredSystemPathBuf("apps/api/src/websocket/index.js.map"), AnchoredSystemPathBuf("apps/api/src/services/DatabaseService.js.map"), AnchoredSystemPathBuf("apps/api/src/websocket/index.d.ts"), AnchoredSystemPathBuf("apps/api/src/websocket/index.d.ts.map"), AnchoredSystemPathBuf("apps/api/src/services/DatabaseService.d.ts"), AnchoredSystemPathBuf("apps/api/src/services/DatabaseService.js"), AnchoredSystemPathBuf("apps/api/src/websocket/index.js"), AnchoredSystemPathBuf("apps/api/src/routes/index.js")}
2025-05-26T01:36:58.879265Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@sfquant/api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-05-26T01:36:59.278227Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/src/services/ExchangeService.js"), AnchoredSystemPathBuf("apps/api/src/services/RedisService.js.map"), AnchoredSystemPathBuf("apps/api/src/services/RedisService.d.ts"), AnchoredSystemPathBuf("apps/api/src/services/ExchangeService.js.map"), AnchoredSystemPathBuf("apps/api/src/services/RedisService.d.ts.map"), AnchoredSystemPathBuf("apps/api/src/services/RedisService.js")}
2025-05-26T01:36:59.278235Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@sfquant/api"), path: AnchoredSystemPathBuf("apps/api") }}))
2025-05-26T01:36:59.378798Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("apps/api/src/index.js"), AnchoredSystemPathBuf("apps/api/src/services/MarketDataService.d.ts.map"), AnchoredSystemPathBuf("apps/api/src/services/MarketDataService.js"), AnchoredSystemPathBuf("tsconfig.tsbuildinfo"), AnchoredSystemPathBuf("apps/api/src/services/StrategyService.js.map"), AnchoredSystemPathBuf("apps/api/src/services/MarketDataService.js.map"), AnchoredSystemPathBuf("apps/api/src/services/StrategyService.d.ts.map"), AnchoredSystemPathBuf("apps/api/src/services/ExchangeService.d.ts.map"), AnchoredSystemPathBuf("apps/api/src/services/ExchangeService.d.ts"), AnchoredSystemPathBuf("apps/api/src/index.d.ts.map"), AnchoredSystemPathBuf("apps/api/src/services/ExchangeService.js"), AnchoredSystemPathBuf("apps/api/src/index.js.map"), AnchoredSystemPathBuf("apps/api/src/services/MarketDataService.d.ts"), AnchoredSystemPathBuf("apps/api/src/services/StrategyService.js"), AnchoredSystemPathBuf("apps/api/src/services/StrategyService.d.ts"), AnchoredSystemPathBuf("apps/api/src/index.d.ts")}
2025-05-26T01:36:59.378806Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@sfquant/api"), path: AnchoredSystemPathBuf("apps/api") }, WorkspacePackage { name: Root, path: AnchoredSystemPathBuf("") }}))
2025-05-26T01:37:12.078759Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/tsconfig.json")}
2025-05-26T01:37:12.078766Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@sfquant/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-05-26T01:37:17.978961Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/ccxt-adapter/tsconfig.json")}
2025-05-26T01:37:17.978971Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@sfquant/ccxt-adapter"), path: AnchoredSystemPathBuf("packages/ccxt-adapter") }}))
2025-05-26T01:37:24.778141Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/dex-adapter/tsconfig.json")}
2025-05-26T01:37:24.778150Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@sfquant/dex-adapter"), path: AnchoredSystemPathBuf("packages/dex-adapter") }}))
2025-05-26T01:37:30.877316Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/strategy-runtime/tsconfig.json")}
2025-05-26T01:37:30.877324Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@sfquant/strategy-runtime"), path: AnchoredSystemPathBuf("packages/strategy-runtime") }}))
2025-05-26T01:37:39.378345Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/src/utils/validation.ts"), AnchoredSystemPathBuf("packages/core/src/utils")}
2025-05-26T01:37:39.378353Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@sfquant/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-05-26T01:37:48.477116Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/src/utils/formatting.ts")}
2025-05-26T01:37:48.477125Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@sfquant/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-05-26T01:38:07.877562Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/dex-adapter/src/utils"), AnchoredSystemPathBuf("packages/dex-adapter/src/utils/tokenUtils.ts")}
2025-05-26T01:38:07.877574Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@sfquant/dex-adapter"), path: AnchoredSystemPathBuf("packages/dex-adapter") }}))
2025-05-26T01:38:25.278064Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/dex-adapter/src/utils/priceUtils.ts")}
2025-05-26T01:38:25.278074Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@sfquant/dex-adapter"), path: AnchoredSystemPathBuf("packages/dex-adapter") }}))
2025-05-26T01:38:31.677044Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/dist")}
2025-05-26T01:38:31.677052Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@sfquant/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-05-26T01:38:31.777594Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/core/tsconfig.tsbuildinfo")}
2025-05-26T01:38:31.777604Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@sfquant/core"), path: AnchoredSystemPathBuf("packages/core") }}))
2025-05-26T01:38:31.777619Z  WARN turborepo_lib::package_changes_watcher: hashes are the same, no need to rerun
2025-05-26T01:38:32.576877Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/strategy-runtime/tsconfig.tsbuildinfo"), AnchoredSystemPathBuf("packages/core/src/utils/formatting.d.ts.map"), AnchoredSystemPathBuf("packages/core/src/index.d.ts"), AnchoredSystemPathBuf("packages/core/src/utils/validation.js"), AnchoredSystemPathBuf("packages/core/src/index.d.ts.map"), AnchoredSystemPathBuf("packages/core/src/types/exchange.js"), AnchoredSystemPathBuf("packages/core/src/index.js.map"), AnchoredSystemPathBuf("packages/core/src/types/exchange.js.map"), AnchoredSystemPathBuf("packages/core/src/utils/formatting.d.ts"), AnchoredSystemPathBuf("packages/core/src/index.js"), AnchoredSystemPathBuf("packages/core/src/types/strategy.d.ts.map"), AnchoredSystemPathBuf("packages/core/src/utils/formatting.js"), AnchoredSystemPathBuf("packages/core/src/utils/formatting.js.map"), AnchoredSystemPathBuf("packages/core/src/utils/validation.d.ts"), AnchoredSystemPathBuf("packages/core/src/utils/validation.js.map"), AnchoredSystemPathBuf("packages/strategy-runtime/dist"), AnchoredSystemPathBuf("packages/core/src/types/exchange.d.ts.map"), AnchoredSystemPathBuf("packages/core/src/utils/validation.d.ts.map"), AnchoredSystemPathBuf("packages/core/src/types/exchange.d.ts"), AnchoredSystemPathBuf("packages/core/src/types/strategy.js"), AnchoredSystemPathBuf("packages/core/src/types/strategy.js.map"), AnchoredSystemPathBuf("packages/core/src/types/strategy.d.ts")}
2025-05-26T01:38:32.576885Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@sfquant/core"), path: AnchoredSystemPathBuf("packages/core") }, WorkspacePackage { name: Other("@sfquant/strategy-runtime"), path: AnchoredSystemPathBuf("packages/strategy-runtime") }}))
2025-05-26T01:39:19.176974Z  WARN turborepo_lib::package_changes_watcher: changed_files: {AnchoredSystemPathBuf("packages/strategy-runtime/tsconfig.json")}
2025-05-26T01:39:19.176983Z  WARN turborepo_lib::package_changes_watcher: changed_packages: Ok(Some({WorkspacePackage { name: Other("@sfquant/strategy-runtime"), path: AnchoredSystemPathBuf("packages/strategy-runtime") }}))
