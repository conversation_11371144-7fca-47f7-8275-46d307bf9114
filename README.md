# SFQuant - Cryptocurrency Quantitative Strategy Management System

一个专注于虚拟货币量化策略开发的管理系统，为策略员提供简易使用的策略编写环境。

## 🚀 特性

### 策略类型支持
- **套利策略** - 跨交易所价差套利
- **趋势策略** - 技术指标驱动的趋势跟踪
- **高频策略** - 微秒级高频交易
- **做市商策略** - 双边报价和流动性提供
- **AI策略** - 机器学习驱动的智能交易

### 交易所支持
- **CEX** - 通过CCXT支持200+中心化交易所
- **DEX** - 支持主流去中心化交易所 (Uniswap, SushiSwap, Curve等)

### 多语言策略开发
- **TypeScript** - 原生支持，完整类型安全
- **Python** - 通过运行时桥接，支持丰富的量化库
- **扩展性** - 架构支持未来添加更多语言

## 🏗️ 架构

```
┌─────────────────────────────────────────────────────────────┐
│                 Strategy Management UI                      │
│                   (Next.js + TS)                           │
├─────────────────────────────────────────────────────────────┤
│                Strategy Execution Engine                    │
│  ┌─────────────────┬─────────────────┬─────────────────┐   │
│  │   TypeScript    │     Python      │   Future Lang   │   │
│  │   Runtime       │    Runtime      │    Runtime      │   │
│  └─────────────────┴─────────────────┴─────────────────┘   │
├─────────────────────────────────────────────────────────────┤
│                 Unified Trading Layer                       │
│  ┌─────────────────┬─────────────────┬─────────────────┐   │
│  │   CEX Adapter   │   DEX Adapter   │  Data Pipeline  │   │
│  │     (CCXT)      │   (Web3 SDK)    │   (Real-time)   │   │
│  └─────────────────┴─────────────────┴─────────────────┘   │
└─────────────────────────────────────────────────────────────┘
```

## 📦 项目结构

```
sfquant/
├── apps/
│   ├── web/                 # Next.js 前端应用
│   ├── api/                 # API 服务
│   └── strategy-engine/     # 策略执行引擎
├── packages/
│   ├── core/               # 核心类型和工具
│   ├── ccxt-adapter/       # CCXT 交易所适配器
│   ├── dex-adapter/        # DEX 适配器
│   ├── strategy-runtime/   # 多语言策略运行时
│   └── ui/                 # 共享UI组件
└── tools/
    ├── eslint-config/      # ESLint 配置
    └── typescript-config/  # TypeScript 配置
```

## 🛠️ 技术栈

### 前端
- **Next.js 14** - React框架
- **TypeScript** - 类型安全
- **Ant Design** - UI组件库
- **Monaco Editor** - 代码编辑器
- **TradingView** - 图表库

### 后端
- **Node.js** - 运行时
- **Fastify** - Web框架
- **CCXT** - 交易所连接
- **ethers.js** - Web3集成
- **PostgreSQL** - 主数据库
- **Redis** - 缓存和消息队列

### 开发工具
- **Turbo** - Monorepo构建工具
- **ESLint** - 代码检查
- **Prettier** - 代码格式化
- **Jest** - 测试框架

## 🚦 快速开始

### 环境要求
- Node.js >= 18.0.0
- Python >= 3.8 (用于Python策略)
- PostgreSQL >= 14
- Redis >= 6

### 安装依赖
```bash
npm install
```

### 启动开发环境
```bash
npm run dev
```

### 构建项目
```bash
npm run build
```

## 📖 文档

- [策略开发指南](./docs/strategy-development.md)
- [API文档](./docs/api.md)
- [部署指南](./docs/deployment.md)

## 🤝 贡献

欢迎提交Issue和Pull Request！

## 📄 许可证

MIT License
