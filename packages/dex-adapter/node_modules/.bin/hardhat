#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/Users/<USER>/CursorSpace/SFQuant/node_modules/.pnpm/hardhat@2.24.0_ts-node@10.9.2_@types+node@20.17.50_typescript@5.8.3__typescript@5.8.3/node_modules/hardhat/internal/cli/node_modules:/Users/<USER>/CursorSpace/SFQuant/node_modules/.pnpm/hardhat@2.24.0_ts-node@10.9.2_@types+node@20.17.50_typescript@5.8.3__typescript@5.8.3/node_modules/hardhat/internal/node_modules:/Users/<USER>/CursorSpace/SFQuant/node_modules/.pnpm/hardhat@2.24.0_ts-node@10.9.2_@types+node@20.17.50_typescript@5.8.3__typescript@5.8.3/node_modules/hardhat/node_modules:/Users/<USER>/CursorSpace/SFQuant/node_modules/.pnpm/hardhat@2.24.0_ts-node@10.9.2_@types+node@20.17.50_typescript@5.8.3__typescript@5.8.3/node_modules:/Users/<USER>/CursorSpace/SFQuant/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/Users/<USER>/CursorSpace/SFQuant/node_modules/.pnpm/hardhat@2.24.0_ts-node@10.9.2_@types+node@20.17.50_typescript@5.8.3__typescript@5.8.3/node_modules/hardhat/internal/cli/node_modules:/Users/<USER>/CursorSpace/SFQuant/node_modules/.pnpm/hardhat@2.24.0_ts-node@10.9.2_@types+node@20.17.50_typescript@5.8.3__typescript@5.8.3/node_modules/hardhat/internal/node_modules:/Users/<USER>/CursorSpace/SFQuant/node_modules/.pnpm/hardhat@2.24.0_ts-node@10.9.2_@types+node@20.17.50_typescript@5.8.3__typescript@5.8.3/node_modules/hardhat/node_modules:/Users/<USER>/CursorSpace/SFQuant/node_modules/.pnpm/hardhat@2.24.0_ts-node@10.9.2_@types+node@20.17.50_typescript@5.8.3__typescript@5.8.3/node_modules:/Users/<USER>/CursorSpace/SFQuant/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../../../node_modules/.pnpm/hardhat@2.24.0_ts-node@10.9.2_@types+node@20.17.50_typescript@5.8.3__typescript@5.8.3/node_modules/hardhat/internal/cli/bootstrap.js" "$@"
else
  exec node  "$basedir/../../../../node_modules/.pnpm/hardhat@2.24.0_ts-node@10.9.2_@types+node@20.17.50_typescript@5.8.3__typescript@5.8.3/node_modules/hardhat/internal/cli/bootstrap.js" "$@"
fi
