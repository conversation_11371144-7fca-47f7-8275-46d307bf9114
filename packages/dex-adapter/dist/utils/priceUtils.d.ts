export declare function calculatePriceImpact(amountIn: string, amountOut: string, reserveIn: string, reserveOut: string): number;
export declare function calculateSlippage(expectedAmount: string, actualAmount: string): number;
export declare function applySlippageTolerance(amount: string, slippageTolerance: number, isMinimum?: boolean): string;
export declare function calculateAmountOut(amountIn: string, reserveIn: string, reserveOut: string, fee?: number): string;
export declare function calculateAmountIn(amountOut: string, reserveIn: string, reserveOut: string, fee?: number): string;
export declare function calculateLiquidityValue(liquidity: string, totalSupply: string, reserve0: string, reserve1: string): {
    amount0: string;
    amount1: string;
};
export declare function calculateOptimalAmounts(amountADesired: string, amountBDesired: string, reserveA: string, reserveB: string): {
    amountA: string;
    amountB: string;
};
export declare function formatPrice(price: number, decimals?: number): string;
//# sourceMappingURL=priceUtils.d.ts.map