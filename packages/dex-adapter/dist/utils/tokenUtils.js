// 常用代币地址映射
export const COMMON_TOKENS = {
    // Ethereum Mainnet
    1: {
        'ETH': {
            address: '******************************************',
            symbol: 'ETH',
            name: '<PERSON>ther',
            decimals: 18,
            chainId: 1
        },
        'WETH': {
            address: '******************************************',
            symbol: 'WETH',
            name: 'Wrapped Ether',
            decimals: 18,
            chainId: 1
        },
        'USDC': {
            address: '0xA0b86a33E6441b8C4505B8C4505B8C4505B8C4505',
            symbol: 'USDC',
            name: 'USD Coin',
            decimals: 6,
            chainId: 1
        },
        'USDT': {
            address: '******************************************',
            symbol: 'USDT',
            name: 'Tether USD',
            decimals: 6,
            chainId: 1
        },
        'DAI': {
            address: '******************************************',
            symbol: 'DAI',
            name: '<PERSON> Stablecoin',
            decimals: 18,
            chainId: 1
        },
        'WBTC': {
            address: '******************************************',
            symbol: 'WBTC',
            name: 'Wrapped BTC',
            decimals: 8,
            chainId: 1
        }
    },
    // BSC
    56: {
        'BNB': {
            address: '******************************************',
            symbol: 'BNB',
            name: 'BNB',
            decimals: 18,
            chainId: 56
        },
        'WBNB': {
            address: '******************************************',
            symbol: 'WBNB',
            name: 'Wrapped BNB',
            decimals: 18,
            chainId: 56
        },
        'USDC': {
            address: '******************************************',
            symbol: 'USDC',
            name: 'USD Coin',
            decimals: 18,
            chainId: 56
        },
        'USDT': {
            address: '******************************************',
            symbol: 'USDT',
            name: 'Tether USD',
            decimals: 18,
            chainId: 56
        }
    },
    // Polygon
    137: {
        'MATIC': {
            address: '******************************************',
            symbol: 'MATIC',
            name: 'Matic Token',
            decimals: 18,
            chainId: 137
        },
        'WMATIC': {
            address: '******************************************',
            symbol: 'WMATIC',
            name: 'Wrapped Matic',
            decimals: 18,
            chainId: 137
        },
        'USDC': {
            address: '******************************************',
            symbol: 'USDC',
            name: 'USD Coin',
            decimals: 6,
            chainId: 137
        },
        'USDT': {
            address: '******************************************',
            symbol: 'USDT',
            name: 'Tether USD',
            decimals: 6,
            chainId: 137
        }
    }
};
export function getTokenBySymbol(symbol, chainId) {
    const chainTokens = COMMON_TOKENS[chainId];
    if (!chainTokens) {
        return null;
    }
    return chainTokens[symbol] || null;
}
export function getTokenByAddress(address, chainId) {
    const chainTokens = COMMON_TOKENS[chainId];
    if (!chainTokens) {
        return null;
    }
    for (const token of Object.values(chainTokens)) {
        if (token.address.toLowerCase() === address.toLowerCase()) {
            return token;
        }
    }
    return null;
}
export function isNativeToken(address) {
    return address === '******************************************';
}
export function getWrappedToken(chainId) {
    const chainTokens = COMMON_TOKENS[chainId];
    if (!chainTokens) {
        return null;
    }
    // 查找包装代币
    const wrappedSymbols = ['WETH', 'WBNB', 'WMATIC'];
    for (const symbol of wrappedSymbols) {
        if (chainTokens[symbol]) {
            return chainTokens[symbol];
        }
    }
    return null;
}
export function formatTokenAmount(amount, decimals) {
    const num = parseFloat(amount);
    const divisor = Math.pow(10, decimals);
    return (num / divisor).toString();
}
export function parseTokenAmount(amount, decimals) {
    const num = parseFloat(amount);
    const multiplier = Math.pow(10, decimals);
    return Math.floor(num * multiplier).toString();
}
//# sourceMappingURL=tokenUtils.js.map