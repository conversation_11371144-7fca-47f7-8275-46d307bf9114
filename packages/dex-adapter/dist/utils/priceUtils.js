// 价格计算工具函数
export function calculatePriceImpact(amountIn, amountOut, reserveIn, reserveOut) {
    const amountInNum = parseFloat(amountIn);
    const amountOutNum = parseFloat(amountOut);
    const reserveInNum = parseFloat(reserveIn);
    const reserveOutNum = parseFloat(reserveOut);
    // 计算当前价格
    const currentPrice = reserveOutNum / reserveInNum;
    // 计算执行价格
    const executionPrice = amountOutNum / amountInNum;
    // 计算价格影响
    const priceImpact = Math.abs((executionPrice - currentPrice) / currentPrice);
    return priceImpact;
}
export function calculateSlippage(expectedAmount, actualAmount) {
    const expected = parseFloat(expectedAmount);
    const actual = parseFloat(actualAmount);
    return Math.abs((actual - expected) / expected);
}
export function applySlippageTolerance(amount, slippageTolerance, isMinimum = true) {
    const amountNum = parseFloat(amount);
    if (isMinimum) {
        // 计算最小接受数量
        return (amountNum * (1 - slippageTolerance)).toString();
    }
    else {
        // 计算最大支付数量
        return (amountNum * (1 + slippageTolerance)).toString();
    }
}
export function calculateAmountOut(amountIn, reserveIn, reserveOut, fee = 3000 // 0.3% in basis points
) {
    const amountInNum = parseFloat(amountIn);
    const reserveInNum = parseFloat(reserveIn);
    const reserveOutNum = parseFloat(reserveOut);
    // 计算手续费后的输入数量
    const amountInWithFee = amountInNum * (10000 - fee);
    // 使用恒定乘积公式计算输出数量
    const numerator = amountInWithFee * reserveOutNum;
    const denominator = (reserveInNum * 10000) + amountInWithFee;
    const amountOut = numerator / denominator;
    return amountOut.toString();
}
export function calculateAmountIn(amountOut, reserveIn, reserveOut, fee = 3000 // 0.3% in basis points
) {
    const amountOutNum = parseFloat(amountOut);
    const reserveInNum = parseFloat(reserveIn);
    const reserveOutNum = parseFloat(reserveOut);
    // 使用恒定乘积公式计算输入数量
    const numerator = reserveInNum * amountOutNum * 10000;
    const denominator = (reserveOutNum - amountOutNum) * (10000 - fee);
    const amountIn = numerator / denominator;
    return amountIn.toString();
}
export function calculateLiquidityValue(liquidity, totalSupply, reserve0, reserve1) {
    const liquidityNum = parseFloat(liquidity);
    const totalSupplyNum = parseFloat(totalSupply);
    const reserve0Num = parseFloat(reserve0);
    const reserve1Num = parseFloat(reserve1);
    const share = liquidityNum / totalSupplyNum;
    return {
        amount0: (reserve0Num * share).toString(),
        amount1: (reserve1Num * share).toString()
    };
}
export function calculateOptimalAmounts(amountADesired, amountBDesired, reserveA, reserveB) {
    const amountADesiredNum = parseFloat(amountADesired);
    const amountBDesiredNum = parseFloat(amountBDesired);
    const reserveANum = parseFloat(reserveA);
    const reserveBNum = parseFloat(reserveB);
    if (reserveANum === 0 && reserveBNum === 0) {
        return {
            amountA: amountADesired,
            amountB: amountBDesired
        };
    }
    // 计算基于A的最优B数量
    const amountBOptimal = (amountADesiredNum * reserveBNum) / reserveANum;
    if (amountBOptimal <= amountBDesiredNum) {
        return {
            amountA: amountADesired,
            amountB: amountBOptimal.toString()
        };
    }
    else {
        // 计算基于B的最优A数量
        const amountAOptimal = (amountBDesiredNum * reserveANum) / reserveBNum;
        return {
            amountA: amountAOptimal.toString(),
            amountB: amountBDesired
        };
    }
}
export function formatPrice(price, decimals = 8) {
    if (price === 0)
        return '0';
    if (price < 0.00001) {
        return price.toExponential(4);
    }
    else if (price < 1) {
        return price.toFixed(8);
    }
    else if (price < 1000) {
        return price.toFixed(4);
    }
    else {
        return price.toFixed(2);
    }
}
//# sourceMappingURL=priceUtils.js.map