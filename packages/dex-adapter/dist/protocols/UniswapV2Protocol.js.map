{"version": 3, "file": "UniswapV2Protocol.js", "sourceRoot": "", "sources": ["../../src/protocols/UniswapV2Protocol.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,MAAM,EAAE,MAAM,QAAQ,CAAA;AAC/B,OAAO,EASL,QAAQ,EACR,0BAA0B,EAC3B,MAAM,cAAc,CAAA;AAErB,MAAM,OAAO,iBAAiB;IAkC5B,YAAY,WAAwB,EAAE,QAAyB;QAjCtD,SAAI,GAAG,YAAY,CAAA;QACnB,YAAO,GAAG,OAAO,CAAA;QAMlB,mBAAc,GAA2B,IAAI,CAAA;QAC7C,oBAAe,GAA2B,IAAI,CAAA;QAEtD,wBAAwB;QACP,eAAU,GAAG;YAC5B,8GAA8G;YAC9G,8GAA8G;YAC9G,kKAAkK;YAClK,kKAAkK;YAClK,4NAA4N;YAC5N,qLAAqL;SACtL,CAAA;QAEgB,gBAAW,GAAG;YAC7B,uFAAuF;YACvF,8DAA8D;YAC9D,wDAAwD;SACzD,CAAA;QAEgB,aAAQ,GAAG;YAC1B,8GAA8G;YAC9G,mDAAmD;YACnD,mDAAmD;YACnD,qDAAqD;SACtD,CAAA;QAGC,IAAI,CAAC,OAAO,GAAG,WAAW,CAAC,OAAO,CAAA;QAClC,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAA;QAExB,cAAc;QACd,MAAM,SAAS,GAAG,IAAI,CAAC,oBAAoB,CAAC,WAAW,CAAC,OAAO,CAAC,CAAA;QAChE,IAAI,CAAC,aAAa,GAAG,SAAS,CAAC,MAAM,CAAA;QACrC,IAAI,CAAC,cAAc,GAAG,SAAS,CAAC,OAAO,CAAA;QAEvC,IAAI,CAAC,mBAAmB,EAAE,CAAA;IAC5B,CAAC;IAEO,oBAAoB,CAAC,OAAe;QAC1C,mBAAmB;QACnB,IAAI,OAAO,KAAK,CAAC,EAAE,CAAC;YAClB,OAAO;gBACL,MAAM,EAAE,4CAA4C;gBACpD,OAAO,EAAE,4CAA4C;aACtD,CAAA;QACH,CAAC;QACD,MAAM;aACD,IAAI,OAAO,KAAK,EAAE,EAAE,CAAC;YACxB,OAAO;gBACL,MAAM,EAAE,4CAA4C,EAAE,cAAc;gBACpE,OAAO,EAAE,4CAA4C;aACtD,CAAA;QACH,CAAC;QACD,UAAU;aACL,IAAI,OAAO,KAAK,GAAG,EAAE,CAAC;YACzB,OAAO;gBACL,MAAM,EAAE,4CAA4C,EAAE,YAAY;gBAClE,OAAO,EAAE,4CAA4C;aACtD,CAAA;QACH,CAAC;QACD,YAAY;aACP,CAAC;YACJ,OAAO;gBACL,MAAM,EAAE,4CAA4C;gBACpD,OAAO,EAAE,4CAA4C;aACtD,CAAA;QACH,CAAC;IACH,CAAC;IAEO,mBAAmB;QACzB,IAAI,CAAC,cAAc,GAAG,IAAI,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAA;QAC7F,IAAI,CAAC,eAAe,GAAG,IAAI,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAA;IAClG,CAAC;IAED,KAAK,CAAC,aAAa,CAAC,YAAoB,EAAE,SAAkB;QAC1D,IAAI,CAAC;YACH,MAAM,gBAAgB,GAAG,SAAS,IAAI,IAAI,CAAC,cAAc,EAAE,CAAA;YAC3D,MAAM,QAAQ,GAAG,MAAM,CAAC,UAAU,CAAC,GAAG,EAAE,EAAE,CAAC,CAAA,CAAC,UAAU;YAEtD,MAAM,IAAI,GAAG,CAAC,YAAY,EAAE,gBAAgB,CAAC,CAAA;YAC7C,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,cAAe,CAAC,aAAa,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAA;YAExE,OAAO,UAAU,CAAC,MAAM,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAA,CAAC,iCAAiC;QACxF,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,QAAQ,CAAC,8BAA8B,KAAK,EAAE,EAAE,IAAI,CAAC,IAAI,CAAC,CAAA;QACtE,CAAC;IACH,CAAC;IAED,KAAK,CAAC,YAAY,CAAC,QAAgB,EAAE,OAAe,EAAE,QAAgB;QACpE,IAAI,CAAC;YACH,MAAM,IAAI,GAAG,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAA;YAChC,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,cAAe,CAAC,aAAa,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAA;YACxE,OAAO,OAAO,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAA;QAC/C,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,QAAQ,CAAC,6BAA6B,KAAK,EAAE,EAAE,IAAI,CAAC,IAAI,CAAC,CAAA;QACrE,CAAC;IACH,CAAC;IAED,KAAK,CAAC,WAAW,CAAC,SAAiB,EAAE,OAAe,EAAE,QAAgB;QACpE,IAAI,CAAC;YACH,MAAM,IAAI,GAAG,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAA;YAChC,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,cAAe,CAAC,YAAY,CAAC,SAAS,EAAE,IAAI,CAAC,CAAA;YACxE,OAAO,OAAO,CAAC,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAA;QAC9B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,QAAQ,CAAC,4BAA4B,KAAK,EAAE,EAAE,IAAI,CAAC,IAAI,CAAC,CAAA;QACpE,CAAC;IACH,CAAC;IAED,KAAK,CAAC,YAAY,CAAC,OAAe,EAAE,QAAgB,EAAE,QAAgB;QACpE,IAAI,CAAC;YACH,SAAS;YACT,MAAM,UAAU,GAAG,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAA;YACtC,IAAI,aAAqB,CAAA;YACzB,IAAI,QAAkB,CAAA;YAEtB,IAAI,CAAC;gBACH,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,cAAe,CAAC,aAAa,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAA;gBAC9E,aAAa,GAAG,OAAO,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAA;gBACtD,QAAQ,GAAG,UAAU,CAAA;YACvB,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,qBAAqB;gBACrB,MAAM,WAAW,GAAG,IAAI,CAAC,cAAc,EAAE,CAAA;gBACzC,MAAM,QAAQ,GAAG,CAAC,OAAO,EAAE,WAAW,EAAE,QAAQ,CAAC,CAAA;gBAEjD,IAAI,CAAC;oBACH,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,cAAe,CAAC,aAAa,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAA;oBAC5E,aAAa,GAAG,OAAO,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAA;oBACtD,QAAQ,GAAG,QAAQ,CAAA;gBACrB,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,MAAM,IAAI,0BAA0B,CAAC,uBAAuB,EAAE,IAAI,CAAC,IAAI,CAAC,CAAA;gBAC1E,CAAC;YACH,CAAC;YAED,aAAa;YACb,MAAM,KAAK,GAAe,EAAE,CAAA;YAC5B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;gBAC7C,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAA;gBAC7D,IAAI,IAAI,EAAE,CAAC;oBACT,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;gBAClB,CAAC;YACH,CAAC;YAED,OAAO;gBACL,OAAO,EAAE,MAAM,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC;gBACzC,QAAQ,EAAE,MAAM,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC;gBAC3C,KAAK;gBACL,iBAAiB,EAAE,aAAa;gBAChC,WAAW,EAAE,MAAM,IAAI,CAAC,oBAAoB,CAAC,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAE,aAAa,CAAC;gBACxF,WAAW,EAAE,QAAQ,EAAE,kBAAkB;gBACzC,KAAK,EAAE,QAAQ;aAChB,CAAA;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,QAAQ,CAAC,6BAA6B,KAAK,EAAE,EAAE,IAAI,CAAC,IAAI,CAAC,CAAA;QACrE,CAAC;IACH,CAAC;IAED,KAAK,CAAC,WAAW,CAAC,OAAe,EAAE,QAAgB;QACjD,MAAM,KAAK,GAAgB,EAAE,CAAA;QAE7B,IAAI,CAAC;YACH,OAAO;YACP,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,QAAQ,EAAE,MAAM,CAAC,UAAU,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC,QAAQ,EAAE,CAAC,CAAA;YACpG,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,CAAA;QACxB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO;QACT,CAAC;QAED,iBAAiB;QAEjB,OAAO,KAAK,CAAA;IACd,CAAC;IAED,KAAK,CAAC,OAAO,CAAC,MAAc,EAAE,MAAc,EAAE,GAAY;QACxD,IAAI,CAAC;YACH,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,eAAgB,CAAC,OAAO,CAAC,MAAM,EAAE,MAAM,CAAC,CAAA;YAEvE,IAAI,WAAW,KAAK,MAAM,CAAC,WAAW,EAAE,CAAC;gBACvC,OAAO,IAAI,CAAA;YACb,CAAC;YAED,MAAM,YAAY,GAAG,IAAI,MAAM,CAAC,QAAQ,CAAC,WAAW,EAAE,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAA;YAEnF,MAAM,CAAC,QAAQ,EAAE,UAAU,EAAE,UAAU,CAAC,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC;gBAC3D,YAAY,CAAC,WAAW,EAAE;gBAC1B,YAAY,CAAC,MAAM,EAAE;gBACrB,YAAY,CAAC,MAAM,EAAE;aACtB,CAAC,CAAA;YAEF,OAAO;gBACL,OAAO,EAAE,WAAW;gBACpB,MAAM,EAAE,MAAM,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC;gBAC3C,MAAM,EAAE,MAAM,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC;gBAC3C,GAAG,EAAE,IAAI,EAAE,uBAAuB;gBAClC,SAAS,EAAE,GAAG,EAAE,eAAe;gBAC/B,QAAQ,EAAE,QAAQ,CAAC,QAAQ,CAAC,QAAQ,EAAE;gBACtC,QAAQ,EAAE,QAAQ,CAAC,QAAQ,CAAC,QAAQ,EAAE;gBACtC,QAAQ,EAAE,YAAY;gBACtB,OAAO,EAAE,OAAO;aACjB,CAAA;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,QAAQ,CAAC,uBAAuB,KAAK,EAAE,EAAE,IAAI,CAAC,IAAI,CAAC,CAAA;QAC/D,CAAC;IACH,CAAC;IAED,KAAK,CAAC,WAAW,CAAC,MAAe,EAAE,MAAe;QAChD,MAAM,KAAK,GAAe,EAAE,CAAA;QAE5B,IAAI,MAAM,IAAI,MAAM,EAAE,CAAC;YACrB,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,MAAM,CAAC,CAAA;YAC/C,IAAI,IAAI,EAAE,CAAC;gBACT,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;YAClB,CAAC;QACH,CAAC;QAED,OAAO,KAAK,CAAA;IACd,CAAC;IAED,KAAK,CAAC,oBAAoB,CAAC,MAAsB;QAC/C,IAAI,CAAC;YACH,+BAA+B;YAC/B,MAAM,IAAI,GAAG,CAAC,MAAM,CAAC,OAAO,EAAE,MAAM,CAAC,QAAQ,CAAC,CAAA;YAE9C,MAAM,IAAI,GAAG,IAAI,CAAC,cAAe,CAAC,SAAS,CAAC,kBAAkB,CAAC,0BAA0B,EAAE;gBACzF,MAAM,CAAC,QAAQ;gBACf,MAAM,CAAC,YAAY;gBACnB,IAAI;gBACJ,MAAM,CAAC,EAAE;gBACT,MAAM,CAAC,QAAQ;aAChB,CAAC,CAAA;YAEF,OAAO;gBACL,EAAE,EAAE,IAAI,CAAC,aAAa;gBACtB,IAAI;gBACJ,KAAK,EAAE,MAAM,CAAC,OAAO,KAAK,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,GAAG;gBACpE,QAAQ,EAAE,MAAM,CAAC,QAAQ,IAAI,QAAQ;gBACrC,QAAQ,EAAE,MAAM,CAAC,QAAQ;aAC1B,CAAA;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,QAAQ,CAAC,qCAAqC,KAAK,EAAE,EAAE,IAAI,CAAC,IAAI,CAAC,CAAA;QAC7E,CAAC;IACH,CAAC;IAED,KAAK,CAAC,eAAe,CAAC,MAAsB;QAC1C,IAAI,CAAC;YACH,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC,CAAA;YAC3D,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,WAAW,CAAC,CAAA;YAE7D,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,UAAU,EAAE,CAAA;YAChD,MAAM,QAAQ,GAAG,OAAO,CAAC,QAAQ,IAAI,MAAM,CAAC,UAAU,CAAC,IAAI,EAAE,MAAM,CAAC,CAAA;YAEpE,MAAM,aAAa,GAAG,QAAQ,GAAG,QAAQ,CAAA;YAEzC,OAAO;gBACL,QAAQ,EAAE,QAAQ,CAAC,QAAQ,EAAE;gBAC7B,QAAQ,EAAE,QAAQ,CAAC,QAAQ,EAAE;gBAC7B,YAAY,EAAE,OAAO,CAAC,YAAY,EAAE,QAAQ,EAAE;gBAC9C,oBAAoB,EAAE,OAAO,CAAC,oBAAoB,EAAE,QAAQ,EAAE;gBAC9D,aAAa,EAAE,MAAM,CAAC,WAAW,CAAC,aAAa,CAAC;aACjD,CAAA;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,QAAQ,CAAC,2BAA2B,KAAK,EAAE,EAAE,IAAI,CAAC,IAAI,CAAC,CAAA;QACnE,CAAC;IACH,CAAC;IAED,KAAK,CAAC,4BAA4B,CAAC,MAAuB;QACxD,IAAI,CAAC;YACH,MAAM,IAAI,GAAG,IAAI,CAAC,cAAe,CAAC,SAAS,CAAC,kBAAkB,CAAC,cAAc,EAAE;gBAC7E,MAAM,CAAC,MAAM;gBACb,MAAM,CAAC,MAAM;gBACb,MAAM,CAAC,OAAO;gBACd,MAAM,CAAC,OAAO;gBACd,MAAM,CAAC,UAAU;gBACjB,MAAM,CAAC,UAAU;gBACjB,MAAM,CAAC,EAAE;gBACT,MAAM,CAAC,QAAQ;aAChB,CAAC,CAAA;YAEF,OAAO;gBACL,EAAE,EAAE,IAAI,CAAC,aAAa;gBACtB,IAAI;gBACJ,KAAK,EAAE,GAAG;gBACV,QAAQ,EAAE,QAAQ;aACnB,CAAA;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,QAAQ,CAAC,8CAA8C,KAAK,EAAE,EAAE,IAAI,CAAC,IAAI,CAAC,CAAA;QACtF,CAAC;IACH,CAAC;IAED,KAAK,CAAC,+BAA+B,CAAC,MAAuB;QAC3D,IAAI,CAAC;YACH,MAAM,IAAI,GAAG,IAAI,CAAC,cAAe,CAAC,SAAS,CAAC,kBAAkB,CAAC,iBAAiB,EAAE;gBAChF,MAAM,CAAC,MAAM;gBACb,MAAM,CAAC,MAAM;gBACb,MAAM,CAAC,OAAO,EAAE,mBAAmB;gBACnC,MAAM,CAAC,UAAU;gBACjB,MAAM,CAAC,UAAU;gBACjB,MAAM,CAAC,EAAE;gBACT,MAAM,CAAC,QAAQ;aAChB,CAAC,CAAA;YAEF,OAAO;gBACL,EAAE,EAAE,IAAI,CAAC,aAAa;gBACtB,IAAI;gBACJ,KAAK,EAAE,GAAG;gBACV,QAAQ,EAAE,QAAQ;aACnB,CAAA;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,QAAQ,CAAC,iDAAiD,KAAK,EAAE,EAAE,IAAI,CAAC,IAAI,CAAC,CAAA;QACzF,CAAC;IACH,CAAC;IAED,SAAS;IACD,KAAK,CAAC,YAAY,CAAC,OAAe;QACxC,OAAO;QACP,OAAO;YACL,OAAO;YACP,MAAM,EAAE,SAAS;YACjB,IAAI,EAAE,eAAe;YACrB,QAAQ,EAAE,EAAE;YACZ,OAAO,EAAE,IAAI,CAAC,OAAO;SACtB,CAAA;IACH,CAAC;IAEO,cAAc;QACpB,IAAI,IAAI,CAAC,OAAO,KAAK,CAAC,EAAE,CAAC;YACvB,OAAO,6CAA6C,CAAA,CAAC,gBAAgB;QACvE,CAAC;aAAM,IAAI,IAAI,CAAC,OAAO,KAAK,EAAE,EAAE,CAAC;YAC/B,OAAO,4CAA4C,CAAA,CAAC,WAAW;QACjE,CAAC;aAAM,IAAI,IAAI,CAAC,OAAO,KAAK,GAAG,EAAE,CAAC;YAChC,OAAO,4CAA4C,CAAA,CAAC,eAAe;QACrE,CAAC;QACD,OAAO,6CAA6C,CAAA;IACtD,CAAC;IAEO,cAAc;QACpB,IAAI,IAAI,CAAC,OAAO,KAAK,CAAC,EAAE,CAAC;YACvB,OAAO,4CAA4C,CAAA,CAAC,gBAAgB;QACtE,CAAC;aAAM,IAAI,IAAI,CAAC,OAAO,KAAK,EAAE,EAAE,CAAC;YAC/B,OAAO,4CAA4C,CAAA,CAAC,WAAW;QACjE,CAAC;aAAM,IAAI,IAAI,CAAC,OAAO,KAAK,GAAG,EAAE,CAAC;YAChC,OAAO,4CAA4C,CAAA,CAAC,iBAAiB;QACvE,CAAC;QACD,OAAO,4CAA4C,CAAA;IACrD,CAAC;IAEO,KAAK,CAAC,oBAAoB,CAAC,OAAe,EAAE,QAAgB,EAAE,QAAgB,EAAE,SAAiB;QACvG,YAAY;QACZ,OAAO,KAAK,CAAA,CAAC,OAAO;IACtB,CAAC;CACF"}