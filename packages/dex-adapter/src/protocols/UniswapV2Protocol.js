import { ethers } from 'ethers';
import { DEXError, InsufficientLiquidityError } from '../types/dex';
export class UniswapV2Protocol {
    constructor(chainConfig, provider) {
        this.name = 'Uniswap V2';
        this.version = '2.0.0';
        this.routerContract = null;
        this.factoryContract = null;
        // Uniswap V2 合约ABI（简化版）
        this.ROUTER_ABI = [
            'function getAmountsOut(uint amountIn, address[] calldata path) external view returns (uint[] memory amounts)',
            'function getAmountsIn(uint amountOut, address[] calldata path) external view returns (uint[] memory amounts)',
            'function swapExactTokensForTokens(uint amountIn, uint amountOutMin, address[] calldata path, address to, uint deadline) external returns (uint[] memory amounts)',
            'function swapTokensForExactTokens(uint amountOut, uint amountInMax, address[] calldata path, address to, uint deadline) external returns (uint[] memory amounts)',
            'function addLiquidity(address tokenA, address tokenB, uint amountADesired, uint amountBDesired, uint amountAMin, uint amountBMin, address to, uint deadline) external returns (uint amountA, uint amountB, uint liquidity)',
            'function removeLiquidity(address tokenA, address tokenB, uint liquidity, uint amountAMin, uint amountBMin, address to, uint deadline) external returns (uint amountA, uint amountB)'
        ];
        this.FACTORY_ABI = [
            'function getPair(address tokenA, address tokenB) external view returns (address pair)',
            'function allPairs(uint) external view returns (address pair)',
            'function allPairsLength() external view returns (uint)'
        ];
        this.PAIR_ABI = [
            'function getReserves() external view returns (uint112 reserve0, uint112 reserve1, uint32 blockTimestampLast)',
            'function token0() external view returns (address)',
            'function token1() external view returns (address)',
            'function totalSupply() external view returns (uint)'
        ];
        this.chainId = chainConfig.chainId;
        this.provider = provider;
        // 根据链ID设置合约地址
        const addresses = this.getContractAddresses(chainConfig.chainId);
        this.routerAddress = addresses.router;
        this.factoryAddress = addresses.factory;
        this.initializeContracts();
    }
    getContractAddresses(chainId) {
        // Ethereum Mainnet
        if (chainId === 1) {
            return {
                router: '******************************************',
                factory: '******************************************'
            };
        }
        // BSC
        else if (chainId === 56) {
            return {
                router: '******************************************', // PancakeSwap
                factory: '******************************************'
            };
        }
        // Polygon
        else if (chainId === 137) {
            return {
                router: '******************************************', // QuickSwap
                factory: '******************************************'
            };
        }
        // 默认使用以太坊地址
        else {
            return {
                router: '******************************************',
                factory: '******************************************'
            };
        }
    }
    initializeContracts() {
        this.routerContract = new ethers.Contract(this.routerAddress, this.ROUTER_ABI, this.provider);
        this.factoryContract = new ethers.Contract(this.factoryAddress, this.FACTORY_ABI, this.provider);
    }
    async getTokenPrice(tokenAddress, baseToken) {
        try {
            const baseTokenAddress = baseToken || this.getUSDCAddress();
            const amountIn = ethers.parseUnits('1', 18); // 1 token
            const path = [tokenAddress, baseTokenAddress];
            const amounts = await this.routerContract.getAmountsOut(amountIn, path);
            return parseFloat(ethers.formatUnits(amounts[1], 6)); // 假设base token是USDC (6 decimals)
        }
        catch (error) {
            throw new DEXError(`Failed to get token price: ${error}`, this.name);
        }
    }
    async getAmountOut(amountIn, tokenIn, tokenOut) {
        try {
            const path = [tokenIn, tokenOut];
            const amounts = await this.routerContract.getAmountsOut(amountIn, path);
            return amounts[amounts.length - 1].toString();
        }
        catch (error) {
            throw new DEXError(`Failed to get amount out: ${error}`, this.name);
        }
    }
    async getAmountIn(amountOut, tokenIn, tokenOut) {
        try {
            const path = [tokenIn, tokenOut];
            const amounts = await this.routerContract.getAmountsIn(amountOut, path);
            return amounts[0].toString();
        }
        catch (error) {
            throw new DEXError(`Failed to get amount in: ${error}`, this.name);
        }
    }
    async findBestPath(tokenIn, tokenOut, amountIn) {
        try {
            // 尝试直接路径
            const directPath = [tokenIn, tokenOut];
            let bestAmountOut;
            let bestPath;
            try {
                const amounts = await this.routerContract.getAmountsOut(amountIn, directPath);
                bestAmountOut = amounts[amounts.length - 1].toString();
                bestPath = directPath;
            }
            catch (error) {
                // 直接路径失败，尝试通过WETH的路径
                const wethAddress = this.getWETHAddress();
                const wethPath = [tokenIn, wethAddress, tokenOut];
                try {
                    const amounts = await this.routerContract.getAmountsOut(amountIn, wethPath);
                    bestAmountOut = amounts[amounts.length - 1].toString();
                    bestPath = wethPath;
                }
                catch (error) {
                    throw new InsufficientLiquidityError(`No trading path found`, this.name);
                }
            }
            // 获取路径中的池子信息
            const pools = [];
            for (let i = 0; i < bestPath.length - 1; i++) {
                const pool = await this.getPool(bestPath[i], bestPath[i + 1]);
                if (pool) {
                    pools.push(pool);
                }
            }
            return {
                tokenIn: await this.getTokenInfo(tokenIn),
                tokenOut: await this.getTokenInfo(tokenOut),
                pools,
                expectedAmountOut: bestAmountOut,
                priceImpact: await this.calculatePriceImpact(tokenIn, tokenOut, amountIn, bestAmountOut),
                gasEstimate: '150000', // V2 gas estimate
                route: bestPath
            };
        }
        catch (error) {
            throw new DEXError(`Failed to find best path: ${error}`, this.name);
        }
    }
    async getAllPaths(tokenIn, tokenOut) {
        const paths = [];
        try {
            // 直接路径
            const directPath = await this.findBestPath(tokenIn, tokenOut, ethers.parseUnits('1', 18).toString());
            paths.push(directPath);
        }
        catch (error) {
            // 忽略错误
        }
        // TODO: 添加更多路径选项
        return paths;
    }
    async getPool(token0, token1, fee) {
        try {
            const pairAddress = await this.factoryContract.getPair(token0, token1);
            if (pairAddress === ethers.ZeroAddress) {
                return null;
            }
            const pairContract = new ethers.Contract(pairAddress, this.PAIR_ABI, this.provider);
            const [reserves, pairToken0, pairToken1] = await Promise.all([
                pairContract.getReserves(),
                pairContract.token0(),
                pairContract.token1()
            ]);
            return {
                address: pairAddress,
                token0: await this.getTokenInfo(pairToken0),
                token1: await this.getTokenInfo(pairToken1),
                fee: 3000, // Uniswap V2 固定0.3%手续费
                liquidity: '0', // V2没有直接的流动性数值
                reserve0: reserves.reserve0.toString(),
                reserve1: reserves.reserve1.toString(),
                protocol: 'uniswap-v2',
                version: '2.0.0'
            };
        }
        catch (error) {
            throw new DEXError(`Failed to get pool: ${error}`, this.name);
        }
    }
    async getAllPools(token0, token1) {
        const pools = [];
        if (token0 && token1) {
            const pool = await this.getPool(token0, token1);
            if (pool) {
                pools.push(pool);
            }
        }
        return pools;
    }
    async buildSwapTransaction(params) {
        try {
            // 构建swapExactTokensForTokens交易
            const path = [params.tokenIn, params.tokenOut];
            const data = this.routerContract.interface.encodeFunctionData('swapExactTokensForTokens', [
                params.amountIn,
                params.amountOutMin,
                path,
                params.to,
                params.deadline
            ]);
            return {
                to: this.routerAddress,
                data,
                value: params.tokenIn === ethers.ZeroAddress ? params.amountIn : '0',
                gasLimit: params.gasLimit || '150000',
                gasPrice: params.gasPrice
            };
        }
        catch (error) {
            throw new DEXError(`Failed to build swap transaction: ${error}`, this.name);
        }
    }
    async estimateSwapGas(params) {
        try {
            const transaction = await this.buildSwapTransaction(params);
            const gasLimit = await this.provider.estimateGas(transaction);
            const feeData = await this.provider.getFeeData();
            const gasPrice = feeData.gasPrice || ethers.parseUnits('20', 'gwei');
            const estimatedCost = gasLimit * gasPrice;
            return {
                gasLimit: gasLimit.toString(),
                gasPrice: gasPrice.toString(),
                maxFeePerGas: feeData.maxFeePerGas?.toString(),
                maxPriorityFeePerGas: feeData.maxPriorityFeePerGas?.toString(),
                estimatedCost: ethers.formatEther(estimatedCost)
            };
        }
        catch (error) {
            throw new DEXError(`Failed to estimate gas: ${error}`, this.name);
        }
    }
    async buildAddLiquidityTransaction(params) {
        try {
            const data = this.routerContract.interface.encodeFunctionData('addLiquidity', [
                params.token0,
                params.token1,
                params.amount0,
                params.amount1,
                params.amount0Min,
                params.amount1Min,
                params.to,
                params.deadline
            ]);
            return {
                to: this.routerAddress,
                data,
                value: '0',
                gasLimit: '200000'
            };
        }
        catch (error) {
            throw new DEXError(`Failed to build add liquidity transaction: ${error}`, this.name);
        }
    }
    async buildRemoveLiquidityTransaction(params) {
        try {
            const data = this.routerContract.interface.encodeFunctionData('removeLiquidity', [
                params.token0,
                params.token1,
                params.amount0, // liquidity amount
                params.amount0Min,
                params.amount1Min,
                params.to,
                params.deadline
            ]);
            return {
                to: this.routerAddress,
                data,
                value: '0',
                gasLimit: '200000'
            };
        }
        catch (error) {
            throw new DEXError(`Failed to build remove liquidity transaction: ${error}`, this.name);
        }
    }
    // 私有辅助方法
    async getTokenInfo(address) {
        // 简化实现
        return {
            address,
            symbol: 'UNKNOWN',
            name: 'Unknown Token',
            decimals: 18,
            chainId: this.chainId
        };
    }
    getUSDCAddress() {
        if (this.chainId === 1) {
            return '0xA0b86a33E6441b8C4505B8C4505B8C4505B8C4505'; // Ethereum USDC
        }
        else if (this.chainId === 56) {
            return '******************************************'; // BSC USDC
        }
        else if (this.chainId === 137) {
            return '******************************************'; // Polygon USDC
        }
        return '0xA0b86a33E6441b8C4505B8C4505B8C4505B8C4505';
    }
    getWETHAddress() {
        if (this.chainId === 1) {
            return '******************************************'; // Ethereum WETH
        }
        else if (this.chainId === 56) {
            return '******************************************'; // BSC WBNB
        }
        else if (this.chainId === 137) {
            return '******************************************'; // Polygon WMATIC
        }
        return '******************************************';
    }
    async calculatePriceImpact(tokenIn, tokenOut, amountIn, amountOut) {
        // 简化的价格影响计算
        return 0.003; // 0.3%
    }
}
//# sourceMappingURL=UniswapV2Protocol.js.map