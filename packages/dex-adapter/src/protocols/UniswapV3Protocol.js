import { ethers } from 'ethers';
import { DEXError, InsufficientLiquidityError } from '../types/dex';
export class UniswapV3Protocol {
    constructor(chainConfig, provider) {
        this.name = 'Uniswap V3';
        this.version = '3.0.0';
        this.routerContract = null;
        this.factoryContract = null;
        this.quoterContract = null;
        // Uniswap V3 合约ABI（简化版）
        this.ROUTER_ABI = [
            'function exactInputSingle((address tokenIn, address tokenOut, uint24 fee, address recipient, uint256 deadline, uint256 amountIn, uint256 amountOutMinimum, uint160 sqrtPriceLimitX96)) external payable returns (uint256 amountOut)',
            'function exactOutputSingle((address tokenIn, address tokenOut, uint24 fee, address recipient, uint256 deadline, uint256 amountOut, uint256 amountInMaximum, uint160 sqrtPriceLimitX96)) external payable returns (uint256 amountIn)'
        ];
        this.FACTORY_ABI = [
            'function getPool(address tokenA, address tokenB, uint24 fee) external view returns (address pool)'
        ];
        this.QUOTER_ABI = [
            'function quoteExactInputSingle(address tokenIn, address tokenOut, uint24 fee, uint256 amountIn, uint160 sqrtPriceLimitX96) external returns (uint256 amountOut)',
            'function quoteExactOutputSingle(address tokenIn, address tokenOut, uint24 fee, uint256 amountOut, uint160 sqrtPriceLimitX96) external returns (uint256 amountIn)'
        ];
        this.POOL_ABI = [
            'function liquidity() external view returns (uint128)',
            'function slot0() external view returns (uint160 sqrtPriceX96, int24 tick, uint16 observationIndex, uint16 observationCardinality, uint16 observationCardinalityNext, uint8 feeProtocol, bool unlocked)',
            'function token0() external view returns (address)',
            'function token1() external view returns (address)',
            'function fee() external view returns (uint24)'
        ];
        // 常用费率层级
        this.FEE_TIERS = [500, 3000, 10000]; // 0.05%, 0.3%, 1%
        this.chainId = chainConfig.chainId;
        this.provider = provider;
        // 根据链ID设置合约地址
        const addresses = this.getContractAddresses(chainConfig.chainId);
        this.routerAddress = addresses.router;
        this.factoryAddress = addresses.factory;
        this.quoterAddress = addresses.quoter;
        this.initializeContracts();
    }
    getContractAddresses(chainId) {
        // Ethereum Mainnet
        if (chainId === 1) {
            return {
                router: '******************************************',
                factory: '******************************************',
                quoter: '******************************************'
            };
        }
        // Polygon
        else if (chainId === 137) {
            return {
                router: '******************************************',
                factory: '******************************************',
                quoter: '******************************************'
            };
        }
        // 默认使用以太坊地址
        else {
            return {
                router: '******************************************',
                factory: '******************************************',
                quoter: '******************************************'
            };
        }
    }
    initializeContracts() {
        this.routerContract = new ethers.Contract(this.routerAddress, this.ROUTER_ABI, this.provider);
        this.factoryContract = new ethers.Contract(this.factoryAddress, this.FACTORY_ABI, this.provider);
        this.quoterContract = new ethers.Contract(this.quoterAddress, this.QUOTER_ABI, this.provider);
    }
    async getTokenPrice(tokenAddress, baseToken) {
        try {
            const baseTokenAddress = baseToken || this.getUSDCAddress();
            const amountIn = ethers.parseUnits('1', 18); // 1 token
            // 尝试不同的费率层级
            for (const fee of this.FEE_TIERS) {
                try {
                    const amountOut = await this.quoterContract.quoteExactInputSingle.staticCall(tokenAddress, baseTokenAddress, fee, amountIn, 0);
                    return parseFloat(ethers.formatUnits(amountOut, 6)); // 假设base token是USDC (6 decimals)
                }
                catch (error) {
                    // 尝试下一个费率层级
                    continue;
                }
            }
            throw new InsufficientLiquidityError(`No liquidity found for token ${tokenAddress}`, this.name);
        }
        catch (error) {
            throw new DEXError(`Failed to get token price: ${error}`, this.name);
        }
    }
    async getAmountOut(amountIn, tokenIn, tokenOut) {
        try {
            // 尝试不同的费率层级，返回最佳结果
            let bestAmountOut = '0';
            for (const fee of this.FEE_TIERS) {
                try {
                    const amountOut = await this.quoterContract.quoteExactInputSingle.staticCall(tokenIn, tokenOut, fee, amountIn, 0);
                    if (amountOut > bestAmountOut) {
                        bestAmountOut = amountOut.toString();
                    }
                }
                catch (error) {
                    // 忽略单个费率层级的错误
                    continue;
                }
            }
            if (bestAmountOut === '0') {
                throw new InsufficientLiquidityError(`No liquidity found for pair`, this.name);
            }
            return bestAmountOut;
        }
        catch (error) {
            throw new DEXError(`Failed to get amount out: ${error}`, this.name);
        }
    }
    async getAmountIn(amountOut, tokenIn, tokenOut) {
        try {
            let bestAmountIn = ethers.MaxUint256.toString();
            for (const fee of this.FEE_TIERS) {
                try {
                    const amountIn = await this.quoterContract.quoteExactOutputSingle.staticCall(tokenIn, tokenOut, fee, amountOut, 0);
                    if (amountIn < bestAmountIn) {
                        bestAmountIn = amountIn.toString();
                    }
                }
                catch (error) {
                    continue;
                }
            }
            if (bestAmountIn === ethers.MaxUint256.toString()) {
                throw new InsufficientLiquidityError(`No liquidity found for pair`, this.name);
            }
            return bestAmountIn;
        }
        catch (error) {
            throw new DEXError(`Failed to get amount in: ${error}`, this.name);
        }
    }
    async findBestPath(tokenIn, tokenOut, amountIn) {
        try {
            let bestPath = null;
            let bestAmountOut = '0';
            let bestFee = 0;
            // 尝试直接交易路径
            for (const fee of this.FEE_TIERS) {
                try {
                    const pool = await this.getPool(tokenIn, tokenOut, fee);
                    if (!pool)
                        continue;
                    const amountOut = await this.getAmountOut(amountIn, tokenIn, tokenOut);
                    if (parseFloat(amountOut) > parseFloat(bestAmountOut)) {
                        bestAmountOut = amountOut;
                        bestFee = fee;
                        bestPath = {
                            tokenIn: await this.getTokenInfo(tokenIn),
                            tokenOut: await this.getTokenInfo(tokenOut),
                            pools: [pool],
                            expectedAmountOut: amountOut,
                            priceImpact: await this.calculatePriceImpact(tokenIn, tokenOut, amountIn, amountOut),
                            gasEstimate: '200000', // 估算gas
                            route: [tokenIn, tokenOut]
                        };
                    }
                }
                catch (error) {
                    continue;
                }
            }
            if (!bestPath) {
                throw new InsufficientLiquidityError(`No trading path found`, this.name);
            }
            return bestPath;
        }
        catch (error) {
            throw new DEXError(`Failed to find best path: ${error}`, this.name);
        }
    }
    async getAllPaths(tokenIn, tokenOut) {
        const paths = [];
        // 直接路径
        for (const fee of this.FEE_TIERS) {
            try {
                const pool = await this.getPool(tokenIn, tokenOut, fee);
                if (pool) {
                    const path = await this.findBestPath(tokenIn, tokenOut, ethers.parseUnits('1', 18).toString());
                    paths.push(path);
                }
            }
            catch (error) {
                continue;
            }
        }
        // TODO: 实现多跳路径查找
        return paths;
    }
    async getPool(token0, token1, fee) {
        try {
            const targetFee = fee || 3000; // 默认0.3%
            const poolAddress = await this.factoryContract.getPool(token0, token1, targetFee);
            if (poolAddress === ethers.ZeroAddress) {
                return null;
            }
            const poolContract = new ethers.Contract(poolAddress, this.POOL_ABI, this.provider);
            const [liquidity, slot0, poolToken0, poolToken1, poolFee] = await Promise.all([
                poolContract.liquidity(),
                poolContract.slot0(),
                poolContract.token0(),
                poolContract.token1(),
                poolContract.fee()
            ]);
            return {
                address: poolAddress,
                token0: await this.getTokenInfo(poolToken0),
                token1: await this.getTokenInfo(poolToken1),
                fee: poolFee,
                liquidity: liquidity.toString(),
                sqrtPriceX96: slot0.sqrtPriceX96.toString(),
                tick: slot0.tick,
                protocol: 'uniswap-v3',
                version: '3.0.0'
            };
        }
        catch (error) {
            throw new DEXError(`Failed to get pool: ${error}`, this.name);
        }
    }
    async getAllPools(token0, token1) {
        const pools = [];
        if (token0 && token1) {
            for (const fee of this.FEE_TIERS) {
                const pool = await this.getPool(token0, token1, fee);
                if (pool) {
                    pools.push(pool);
                }
            }
        }
        return pools;
    }
    async buildSwapTransaction(params) {
        try {
            // 构建exactInputSingle交易
            const swapParams = {
                tokenIn: params.tokenIn,
                tokenOut: params.tokenOut,
                fee: 3000, // 默认0.3%费率
                recipient: params.to,
                deadline: params.deadline,
                amountIn: params.amountIn,
                amountOutMinimum: params.amountOutMin,
                sqrtPriceLimitX96: 0
            };
            const data = this.routerContract.interface.encodeFunctionData('exactInputSingle', [swapParams]);
            return {
                to: this.routerAddress,
                data,
                value: params.tokenIn === ethers.ZeroAddress ? params.amountIn : '0',
                gasLimit: params.gasLimit || '200000',
                gasPrice: params.gasPrice
            };
        }
        catch (error) {
            throw new DEXError(`Failed to build swap transaction: ${error}`, this.name);
        }
    }
    async estimateSwapGas(params) {
        try {
            const transaction = await this.buildSwapTransaction(params);
            const gasLimit = await this.provider.estimateGas(transaction);
            const feeData = await this.provider.getFeeData();
            const gasPrice = feeData.gasPrice || ethers.parseUnits('20', 'gwei');
            const estimatedCost = gasLimit * gasPrice;
            return {
                gasLimit: gasLimit.toString(),
                gasPrice: gasPrice.toString(),
                maxFeePerGas: feeData.maxFeePerGas?.toString(),
                maxPriorityFeePerGas: feeData.maxPriorityFeePerGas?.toString(),
                estimatedCost: ethers.formatEther(estimatedCost)
            };
        }
        catch (error) {
            throw new DEXError(`Failed to estimate gas: ${error}`, this.name);
        }
    }
    async buildAddLiquidityTransaction(params) {
        // Uniswap V3 流动性添加比较复杂，需要Position Manager
        throw new DEXError('Add liquidity not implemented for Uniswap V3', this.name);
    }
    async buildRemoveLiquidityTransaction(params) {
        // Uniswap V3 流动性移除
        throw new DEXError('Remove liquidity not implemented for Uniswap V3', this.name);
    }
    // 私有辅助方法
    async getTokenInfo(address) {
        // 这里应该从链上或代币列表获取代币信息
        // 简化实现
        return {
            address,
            symbol: 'UNKNOWN',
            name: 'Unknown Token',
            decimals: 18,
            chainId: this.chainId
        };
    }
    getUSDCAddress() {
        // 根据链ID返回USDC地址
        if (this.chainId === 1) {
            return '0xA0b86a33E6441b8C4505B8C4505B8C4505B8C4505'; // Ethereum USDC
        }
        else if (this.chainId === 137) {
            return '******************************************'; // Polygon USDC
        }
        return '0xA0b86a33E6441b8C4505B8C4505B8C4505B8C4505'; // 默认
    }
    async calculatePriceImpact(tokenIn, tokenOut, amountIn, amountOut) {
        // 简化的价格影响计算
        // 实际实现需要考虑池子的流动性深度
        return 0.001; // 0.1%
    }
}
//# sourceMappingURL=UniswapV3Protocol.js.map