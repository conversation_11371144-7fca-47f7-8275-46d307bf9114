{"version": 3, "file": "UniswapV3Protocol.js", "sourceRoot": "", "sources": ["UniswapV3Protocol.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,MAAM,EAAE,MAAM,QAAQ,CAAA;AAC/B,OAAO,EASL,QAAQ,EACR,0BAA0B,EAC3B,MAAM,cAAc,CAAA;AAErB,MAAM,OAAO,iBAAiB;IAuC5B,YAAY,WAAwB,EAAE,QAAyB;QAtCtD,SAAI,GAAG,YAAY,CAAA;QACnB,YAAO,GAAG,OAAO,CAAA;QAOlB,mBAAc,GAA2B,IAAI,CAAA;QAC7C,oBAAe,GAA2B,IAAI,CAAA;QAC9C,mBAAc,GAA2B,IAAI,CAAA;QAErD,wBAAwB;QACP,eAAU,GAAG;YAC5B,qOAAqO;YACrO,qOAAqO;SACtO,CAAA;QAEgB,gBAAW,GAAG;YAC7B,mGAAmG;SACpG,CAAA;QAEgB,eAAU,GAAG;YAC5B,iKAAiK;YACjK,kKAAkK;SACnK,CAAA;QAEgB,aAAQ,GAAG;YAC1B,sDAAsD;YACtD,wMAAwM;YACxM,mDAAmD;YACnD,mDAAmD;YACnD,+CAA+C;SAChD,CAAA;QAED,SAAS;QACQ,cAAS,GAAG,CAAC,GAAG,EAAE,IAAI,EAAE,KAAK,CAAC,CAAA,CAAC,kBAAkB;QAGhE,IAAI,CAAC,OAAO,GAAG,WAAW,CAAC,OAAO,CAAA;QAClC,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAA;QAExB,cAAc;QACd,MAAM,SAAS,GAAG,IAAI,CAAC,oBAAoB,CAAC,WAAW,CAAC,OAAO,CAAC,CAAA;QAChE,IAAI,CAAC,aAAa,GAAG,SAAS,CAAC,MAAM,CAAA;QACrC,IAAI,CAAC,cAAc,GAAG,SAAS,CAAC,OAAO,CAAA;QACvC,IAAI,CAAC,aAAa,GAAG,SAAS,CAAC,MAAM,CAAA;QAErC,IAAI,CAAC,mBAAmB,EAAE,CAAA;IAC5B,CAAC;IAEO,oBAAoB,CAAC,OAAe;QAC1C,mBAAmB;QACnB,IAAI,OAAO,KAAK,CAAC,EAAE,CAAC;YAClB,OAAO;gBACL,MAAM,EAAE,4CAA4C;gBACpD,OAAO,EAAE,4CAA4C;gBACrD,MAAM,EAAE,4CAA4C;aACrD,CAAA;QACH,CAAC;QACD,UAAU;aACL,IAAI,OAAO,KAAK,GAAG,EAAE,CAAC;YACzB,OAAO;gBACL,MAAM,EAAE,4CAA4C;gBACpD,OAAO,EAAE,4CAA4C;gBACrD,MAAM,EAAE,4CAA4C;aACrD,CAAA;QACH,CAAC;QACD,YAAY;aACP,CAAC;YACJ,OAAO;gBACL,MAAM,EAAE,4CAA4C;gBACpD,OAAO,EAAE,4CAA4C;gBACrD,MAAM,EAAE,4CAA4C;aACrD,CAAA;QACH,CAAC;IACH,CAAC;IAEO,mBAAmB;QACzB,IAAI,CAAC,cAAc,GAAG,IAAI,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAA;QAC7F,IAAI,CAAC,eAAe,GAAG,IAAI,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAA;QAChG,IAAI,CAAC,cAAc,GAAG,IAAI,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAA;IAC/F,CAAC;IAED,KAAK,CAAC,aAAa,CAAC,YAAoB,EAAE,SAAkB;QAC1D,IAAI,CAAC;YACH,MAAM,gBAAgB,GAAG,SAAS,IAAI,IAAI,CAAC,cAAc,EAAE,CAAA;YAC3D,MAAM,QAAQ,GAAG,MAAM,CAAC,UAAU,CAAC,GAAG,EAAE,EAAE,CAAC,CAAA,CAAC,UAAU;YAEtD,YAAY;YACZ,KAAK,MAAM,GAAG,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;gBACjC,IAAI,CAAC;oBACH,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,cAAe,CAAC,qBAAqB,CAAC,UAAU,CAC3E,YAAY,EACZ,gBAAgB,EAChB,GAAG,EACH,QAAQ,EACR,CAAC,CACF,CAAA;oBAED,OAAO,UAAU,CAAC,MAAM,CAAC,WAAW,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC,CAAA,CAAC,iCAAiC;gBACvF,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,YAAY;oBACZ,SAAQ;gBACV,CAAC;YACH,CAAC;YAED,MAAM,IAAI,0BAA0B,CAAC,gCAAgC,YAAY,EAAE,EAAE,IAAI,CAAC,IAAI,CAAC,CAAA;QACjG,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,QAAQ,CAAC,8BAA8B,KAAK,EAAE,EAAE,IAAI,CAAC,IAAI,CAAC,CAAA;QACtE,CAAC;IACH,CAAC;IAED,KAAK,CAAC,YAAY,CAAC,QAAgB,EAAE,OAAe,EAAE,QAAgB;QACpE,IAAI,CAAC;YACH,mBAAmB;YACnB,IAAI,aAAa,GAAG,GAAG,CAAA;YAEvB,KAAK,MAAM,GAAG,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;gBACjC,IAAI,CAAC;oBACH,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,cAAe,CAAC,qBAAqB,CAAC,UAAU,CAC3E,OAAO,EACP,QAAQ,EACR,GAAG,EACH,QAAQ,EACR,CAAC,CACF,CAAA;oBAED,IAAI,SAAS,GAAG,aAAa,EAAE,CAAC;wBAC9B,aAAa,GAAG,SAAS,CAAC,QAAQ,EAAE,CAAA;oBACtC,CAAC;gBACH,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,cAAc;oBACd,SAAQ;gBACV,CAAC;YACH,CAAC;YAED,IAAI,aAAa,KAAK,GAAG,EAAE,CAAC;gBAC1B,MAAM,IAAI,0BAA0B,CAAC,6BAA6B,EAAE,IAAI,CAAC,IAAI,CAAC,CAAA;YAChF,CAAC;YAED,OAAO,aAAa,CAAA;QACtB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,QAAQ,CAAC,6BAA6B,KAAK,EAAE,EAAE,IAAI,CAAC,IAAI,CAAC,CAAA;QACrE,CAAC;IACH,CAAC;IAED,KAAK,CAAC,WAAW,CAAC,SAAiB,EAAE,OAAe,EAAE,QAAgB;QACpE,IAAI,CAAC;YACH,IAAI,YAAY,GAAG,MAAM,CAAC,UAAU,CAAC,QAAQ,EAAE,CAAA;YAE/C,KAAK,MAAM,GAAG,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;gBACjC,IAAI,CAAC;oBACH,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,cAAe,CAAC,sBAAsB,CAAC,UAAU,CAC3E,OAAO,EACP,QAAQ,EACR,GAAG,EACH,SAAS,EACT,CAAC,CACF,CAAA;oBAED,IAAI,QAAQ,GAAG,YAAY,EAAE,CAAC;wBAC5B,YAAY,GAAG,QAAQ,CAAC,QAAQ,EAAE,CAAA;oBACpC,CAAC;gBACH,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,SAAQ;gBACV,CAAC;YACH,CAAC;YAED,IAAI,YAAY,KAAK,MAAM,CAAC,UAAU,CAAC,QAAQ,EAAE,EAAE,CAAC;gBAClD,MAAM,IAAI,0BAA0B,CAAC,6BAA6B,EAAE,IAAI,CAAC,IAAI,CAAC,CAAA;YAChF,CAAC;YAED,OAAO,YAAY,CAAA;QACrB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,QAAQ,CAAC,4BAA4B,KAAK,EAAE,EAAE,IAAI,CAAC,IAAI,CAAC,CAAA;QACpE,CAAC;IACH,CAAC;IAED,KAAK,CAAC,YAAY,CAAC,OAAe,EAAE,QAAgB,EAAE,QAAgB;QACpE,IAAI,CAAC;YACH,IAAI,QAAQ,GAAqB,IAAI,CAAA;YACrC,IAAI,aAAa,GAAG,GAAG,CAAA;YACvB,IAAI,OAAO,GAAG,CAAC,CAAA;YAEf,WAAW;YACX,KAAK,MAAM,GAAG,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;gBACjC,IAAI,CAAC;oBACH,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,QAAQ,EAAE,GAAG,CAAC,CAAA;oBACvD,IAAI,CAAC,IAAI;wBAAE,SAAQ;oBAEnB,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,QAAQ,EAAE,OAAO,EAAE,QAAQ,CAAC,CAAA;oBAEtE,IAAI,UAAU,CAAC,SAAS,CAAC,GAAG,UAAU,CAAC,aAAa,CAAC,EAAE,CAAC;wBACtD,aAAa,GAAG,SAAS,CAAA;wBACzB,OAAO,GAAG,GAAG,CAAA;wBAEb,QAAQ,GAAG;4BACT,OAAO,EAAE,MAAM,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC;4BACzC,QAAQ,EAAE,MAAM,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC;4BAC3C,KAAK,EAAE,CAAC,IAAI,CAAC;4BACb,iBAAiB,EAAE,SAAS;4BAC5B,WAAW,EAAE,MAAM,IAAI,CAAC,oBAAoB,CAAC,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAE,SAAS,CAAC;4BACpF,WAAW,EAAE,QAAQ,EAAE,QAAQ;4BAC/B,KAAK,EAAE,CAAC,OAAO,EAAE,QAAQ,CAAC;yBAC3B,CAAA;oBACH,CAAC;gBACH,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,SAAQ;gBACV,CAAC;YACH,CAAC;YAED,IAAI,CAAC,QAAQ,EAAE,CAAC;gBACd,MAAM,IAAI,0BAA0B,CAAC,uBAAuB,EAAE,IAAI,CAAC,IAAI,CAAC,CAAA;YAC1E,CAAC;YAED,OAAO,QAAQ,CAAA;QACjB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,QAAQ,CAAC,6BAA6B,KAAK,EAAE,EAAE,IAAI,CAAC,IAAI,CAAC,CAAA;QACrE,CAAC;IACH,CAAC;IAED,KAAK,CAAC,WAAW,CAAC,OAAe,EAAE,QAAgB;QACjD,MAAM,KAAK,GAAgB,EAAE,CAAA;QAE7B,OAAO;QACP,KAAK,MAAM,GAAG,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YACjC,IAAI,CAAC;gBACH,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,QAAQ,EAAE,GAAG,CAAC,CAAA;gBACvD,IAAI,IAAI,EAAE,CAAC;oBACT,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,QAAQ,EAAE,MAAM,CAAC,UAAU,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC,QAAQ,EAAE,CAAC,CAAA;oBAC9F,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;gBAClB,CAAC;YACH,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,SAAQ;YACV,CAAC;QACH,CAAC;QAED,iBAAiB;QAEjB,OAAO,KAAK,CAAA;IACd,CAAC;IAED,KAAK,CAAC,OAAO,CAAC,MAAc,EAAE,MAAc,EAAE,GAAY;QACxD,IAAI,CAAC;YACH,MAAM,SAAS,GAAG,GAAG,IAAI,IAAI,CAAA,CAAC,SAAS;YACvC,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,eAAgB,CAAC,OAAO,CAAC,MAAM,EAAE,MAAM,EAAE,SAAS,CAAC,CAAA;YAElF,IAAI,WAAW,KAAK,MAAM,CAAC,WAAW,EAAE,CAAC;gBACvC,OAAO,IAAI,CAAA;YACb,CAAC;YAED,MAAM,YAAY,GAAG,IAAI,MAAM,CAAC,QAAQ,CAAC,WAAW,EAAE,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAA;YAEnF,MAAM,CAAC,SAAS,EAAE,KAAK,EAAE,UAAU,EAAE,UAAU,EAAE,OAAO,CAAC,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC;gBAC5E,YAAY,CAAC,SAAS,EAAE;gBACxB,YAAY,CAAC,KAAK,EAAE;gBACpB,YAAY,CAAC,MAAM,EAAE;gBACrB,YAAY,CAAC,MAAM,EAAE;gBACrB,YAAY,CAAC,GAAG,EAAE;aACnB,CAAC,CAAA;YAEF,OAAO;gBACL,OAAO,EAAE,WAAW;gBACpB,MAAM,EAAE,MAAM,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC;gBAC3C,MAAM,EAAE,MAAM,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC;gBAC3C,GAAG,EAAE,OAAO;gBACZ,SAAS,EAAE,SAAS,CAAC,QAAQ,EAAE;gBAC/B,YAAY,EAAE,KAAK,CAAC,YAAY,CAAC,QAAQ,EAAE;gBAC3C,IAAI,EAAE,KAAK,CAAC,IAAI;gBAChB,QAAQ,EAAE,YAAY;gBACtB,OAAO,EAAE,OAAO;aACjB,CAAA;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,QAAQ,CAAC,uBAAuB,KAAK,EAAE,EAAE,IAAI,CAAC,IAAI,CAAC,CAAA;QAC/D,CAAC;IACH,CAAC;IAED,KAAK,CAAC,WAAW,CAAC,MAAe,EAAE,MAAe;QAChD,MAAM,KAAK,GAAe,EAAE,CAAA;QAE5B,IAAI,MAAM,IAAI,MAAM,EAAE,CAAC;YACrB,KAAK,MAAM,GAAG,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;gBACjC,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,MAAM,EAAE,GAAG,CAAC,CAAA;gBACpD,IAAI,IAAI,EAAE,CAAC;oBACT,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;gBAClB,CAAC;YACH,CAAC;QACH,CAAC;QAED,OAAO,KAAK,CAAA;IACd,CAAC;IAED,KAAK,CAAC,oBAAoB,CAAC,MAAsB;QAC/C,IAAI,CAAC;YACH,uBAAuB;YACvB,MAAM,UAAU,GAAG;gBACjB,OAAO,EAAE,MAAM,CAAC,OAAO;gBACvB,QAAQ,EAAE,MAAM,CAAC,QAAQ;gBACzB,GAAG,EAAE,IAAI,EAAE,WAAW;gBACtB,SAAS,EAAE,MAAM,CAAC,EAAE;gBACpB,QAAQ,EAAE,MAAM,CAAC,QAAQ;gBACzB,QAAQ,EAAE,MAAM,CAAC,QAAQ;gBACzB,gBAAgB,EAAE,MAAM,CAAC,YAAY;gBACrC,iBAAiB,EAAE,CAAC;aACrB,CAAA;YAED,MAAM,IAAI,GAAG,IAAI,CAAC,cAAe,CAAC,SAAS,CAAC,kBAAkB,CAAC,kBAAkB,EAAE,CAAC,UAAU,CAAC,CAAC,CAAA;YAEhG,OAAO;gBACL,EAAE,EAAE,IAAI,CAAC,aAAa;gBACtB,IAAI;gBACJ,KAAK,EAAE,MAAM,CAAC,OAAO,KAAK,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,GAAG;gBACpE,QAAQ,EAAE,MAAM,CAAC,QAAQ,IAAI,QAAQ;gBACrC,QAAQ,EAAE,MAAM,CAAC,QAAQ;aAC1B,CAAA;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,QAAQ,CAAC,qCAAqC,KAAK,EAAE,EAAE,IAAI,CAAC,IAAI,CAAC,CAAA;QAC7E,CAAC;IACH,CAAC;IAED,KAAK,CAAC,eAAe,CAAC,MAAsB;QAC1C,IAAI,CAAC;YACH,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC,CAAA;YAC3D,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,WAAW,CAAC,CAAA;YAE7D,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,UAAU,EAAE,CAAA;YAChD,MAAM,QAAQ,GAAG,OAAO,CAAC,QAAQ,IAAI,MAAM,CAAC,UAAU,CAAC,IAAI,EAAE,MAAM,CAAC,CAAA;YAEpE,MAAM,aAAa,GAAG,QAAQ,GAAG,QAAQ,CAAA;YAEzC,OAAO;gBACL,QAAQ,EAAE,QAAQ,CAAC,QAAQ,EAAE;gBAC7B,QAAQ,EAAE,QAAQ,CAAC,QAAQ,EAAE;gBAC7B,YAAY,EAAE,OAAO,CAAC,YAAY,EAAE,QAAQ,EAAE;gBAC9C,oBAAoB,EAAE,OAAO,CAAC,oBAAoB,EAAE,QAAQ,EAAE;gBAC9D,aAAa,EAAE,MAAM,CAAC,WAAW,CAAC,aAAa,CAAC;aACjD,CAAA;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,QAAQ,CAAC,2BAA2B,KAAK,EAAE,EAAE,IAAI,CAAC,IAAI,CAAC,CAAA;QACnE,CAAC;IACH,CAAC;IAED,KAAK,CAAC,4BAA4B,CAAC,MAAuB;QACxD,0CAA0C;QAC1C,MAAM,IAAI,QAAQ,CAAC,8CAA8C,EAAE,IAAI,CAAC,IAAI,CAAC,CAAA;IAC/E,CAAC;IAED,KAAK,CAAC,+BAA+B,CAAC,MAAuB;QAC3D,mBAAmB;QACnB,MAAM,IAAI,QAAQ,CAAC,iDAAiD,EAAE,IAAI,CAAC,IAAI,CAAC,CAAA;IAClF,CAAC;IAED,SAAS;IACD,KAAK,CAAC,YAAY,CAAC,OAAe;QACxC,qBAAqB;QACrB,OAAO;QACP,OAAO;YACL,OAAO;YACP,MAAM,EAAE,SAAS;YACjB,IAAI,EAAE,eAAe;YACrB,QAAQ,EAAE,EAAE;YACZ,OAAO,EAAE,IAAI,CAAC,OAAO;SACtB,CAAA;IACH,CAAC;IAEO,cAAc;QACpB,gBAAgB;QAChB,IAAI,IAAI,CAAC,OAAO,KAAK,CAAC,EAAE,CAAC;YACvB,OAAO,6CAA6C,CAAA,CAAC,gBAAgB;QACvE,CAAC;aAAM,IAAI,IAAI,CAAC,OAAO,KAAK,GAAG,EAAE,CAAC;YAChC,OAAO,4CAA4C,CAAA,CAAC,eAAe;QACrE,CAAC;QACD,OAAO,6CAA6C,CAAA,CAAC,KAAK;IAC5D,CAAC;IAEO,KAAK,CAAC,oBAAoB,CAAC,OAAe,EAAE,QAAgB,EAAE,QAAgB,EAAE,SAAiB;QACvG,YAAY;QACZ,mBAAmB;QACnB,OAAO,KAAK,CAAA,CAAC,OAAO;IACtB,CAAC;CACF"}