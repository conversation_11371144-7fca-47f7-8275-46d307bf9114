import { z } from 'zod';
export declare const ChainConfig: any;
export type ChainConfig = z.infer<typeof ChainConfig>;
export declare const TokenInfo: any;
export type TokenInfo = z.infer<typeof TokenInfo>;
export declare const PoolInfo: any;
export type PoolInfo = z.infer<typeof PoolInfo>;
export declare const TradePath: any;
export type TradePath = z.infer<typeof TradePath>;
export declare const DEXTradeParams: any;
export type DEXTradeParams = z.infer<typeof DEXTradeParams>;
export declare const GasEstimate: any;
export type GasEstimate = z.infer<typeof GasEstimate>;
export declare const LiquidityParams: any;
export type LiquidityParams = z.infer<typeof LiquidityParams>;
export declare const FlashLoanParams: any;
export type FlashLoanParams = z.infer<typeof FlashLoanParams>;
export interface DEXProtocol {
    readonly name: string;
    readonly version: string;
    readonly chainId: number;
    readonly routerAddress: string;
    readonly factoryAddress: string;
    getTokenPrice(tokenAddress: string, baseToken?: string): Promise<number>;
    getAmountOut(amountIn: string, tokenIn: string, tokenOut: string): Promise<string>;
    getAmountIn(amountOut: string, tokenIn: string, tokenOut: string): Promise<string>;
    findBestPath(tokenIn: string, tokenOut: string, amountIn: string): Promise<TradePath>;
    getAllPaths(tokenIn: string, tokenOut: string): Promise<TradePath[]>;
    getPool(token0: string, token1: string, fee?: number): Promise<PoolInfo | null>;
    getAllPools(token0?: string, token1?: string): Promise<PoolInfo[]>;
    buildSwapTransaction(params: DEXTradeParams): Promise<any>;
    estimateSwapGas(params: DEXTradeParams): Promise<GasEstimate>;
    buildAddLiquidityTransaction(params: LiquidityParams): Promise<any>;
    buildRemoveLiquidityTransaction(params: LiquidityParams): Promise<any>;
    buildFlashLoanTransaction?(params: FlashLoanParams): Promise<any>;
}
export declare const SupportedDEX: any;
export type SupportedDEX = z.infer<typeof SupportedDEX>;
export declare const SupportedChain: any;
export type SupportedChain = z.infer<typeof SupportedChain>;
export declare class DEXError extends Error {
    readonly protocol: string;
    readonly code?: string | undefined;
    readonly details?: any | undefined;
    constructor(message: string, protocol: string, code?: string | undefined, details?: any | undefined);
}
export declare class InsufficientLiquidityError extends DEXError {
    constructor(message: string, protocol: string, details?: any);
}
export declare class SlippageExceededError extends DEXError {
    constructor(message: string, protocol: string, details?: any);
}
export declare class GasEstimationError extends DEXError {
    constructor(message: string, protocol: string, details?: any);
}
//# sourceMappingURL=dex.d.ts.map