import { z } from 'zod'

// 区块链网络配置
export const ChainConfig = z.object({
  chainId: z.number(),
  name: z.string(),
  rpcUrl: z.string(),
  blockExplorer: z.string().optional(),
  nativeCurrency: z.object({
    name: z.string(),
    symbol: z.string(),
    decimals: z.number()
  }),
  testnet: z.boolean().default(false)
})
export type ChainConfig = z.infer<typeof ChainConfig>

// 代币信息
export const TokenInfo = z.object({
  address: z.string(),
  symbol: z.string(),
  name: z.string(),
  decimals: z.number(),
  chainId: z.number(),
  logoURI: z.string().optional(),
  tags: z.array(z.string()).optional()
})
export type TokenInfo = z.infer<typeof TokenInfo>

// 流动性池信息
export const PoolInfo = z.object({
  address: z.string(),
  token0: TokenInfo,
  token1: TokenInfo,
  fee: z.number(), // 手续费，以基点为单位 (例如 3000 = 0.3%)
  liquidity: z.string(), // 流动性数量
  sqrtPriceX96: z.string().optional(), // Uniswap V3 价格
  tick: z.number().optional(), // Uniswap V3 tick
  reserve0: z.string().optional(), // Uniswap V2 储备量
  reserve1: z.string().optional(), // Uniswap V2 储备量
  protocol: z.enum(['uniswap-v2', 'uniswap-v3', 'sushiswap', 'curve', 'balancer']),
  version: z.string().optional()
})
export type PoolInfo = z.infer<typeof PoolInfo>

// 交易路径
export const TradePath = z.object({
  tokenIn: TokenInfo,
  tokenOut: TokenInfo,
  pools: z.array(PoolInfo),
  expectedAmountOut: z.string(),
  priceImpact: z.number(),
  gasEstimate: z.string(),
  route: z.array(z.string()) // 代币地址路径
})
export type TradePath = z.infer<typeof TradePath>

// DEX交易参数
export const DEXTradeParams = z.object({
  tokenIn: z.string(), // 输入代币地址
  tokenOut: z.string(), // 输出代币地址
  amountIn: z.string(), // 输入数量
  amountOutMin: z.string(), // 最小输出数量
  to: z.string(), // 接收地址
  deadline: z.number(), // 截止时间
  slippageTolerance: z.number().default(0.005), // 滑点容忍度 (0.5%)
  gasLimit: z.string().optional(),
  gasPrice: z.string().optional(),
  maxFeePerGas: z.string().optional(),
  maxPriorityFeePerGas: z.string().optional()
})
export type DEXTradeParams = z.infer<typeof DEXTradeParams>

// Gas费用估算
export const GasEstimate = z.object({
  gasLimit: z.string(),
  gasPrice: z.string(),
  maxFeePerGas: z.string().optional(),
  maxPriorityFeePerGas: z.string().optional(),
  estimatedCost: z.string(), // 预估费用（以ETH为单位）
  estimatedCostUSD: z.number().optional()
})
export type GasEstimate = z.infer<typeof GasEstimate>

// 流动性操作参数
export const LiquidityParams = z.object({
  token0: z.string(),
  token1: z.string(),
  amount0: z.string(),
  amount1: z.string(),
  amount0Min: z.string(),
  amount1Min: z.string(),
  to: z.string(),
  deadline: z.number(),
  fee: z.number().optional(), // Uniswap V3 fee tier
  tickLower: z.number().optional(), // Uniswap V3 price range
  tickUpper: z.number().optional()
})
export type LiquidityParams = z.infer<typeof LiquidityParams>

// 闪电贷参数
export const FlashLoanParams = z.object({
  asset: z.string(), // 借贷资产地址
  amount: z.string(), // 借贷数量
  params: z.string(), // 编码的参数
  initiator: z.string(), // 发起者地址
  referralCode: z.number().default(0)
})
export type FlashLoanParams = z.infer<typeof FlashLoanParams>

// DEX协议接口
export interface DEXProtocol {
  readonly name: string
  readonly version: string
  readonly chainId: number
  readonly routerAddress: string
  readonly factoryAddress: string

  // 价格查询
  getTokenPrice(tokenAddress: string, baseToken?: string): Promise<number>
  getAmountOut(amountIn: string, tokenIn: string, tokenOut: string): Promise<string>
  getAmountIn(amountOut: string, tokenIn: string, tokenOut: string): Promise<string>
  
  // 路径查询
  findBestPath(tokenIn: string, tokenOut: string, amountIn: string): Promise<TradePath>
  getAllPaths(tokenIn: string, tokenOut: string): Promise<TradePath[]>
  
  // 流动性池
  getPool(token0: string, token1: string, fee?: number): Promise<PoolInfo | null>
  getAllPools(token0?: string, token1?: string): Promise<PoolInfo[]>
  
  // 交易操作
  buildSwapTransaction(params: DEXTradeParams): Promise<any>
  estimateSwapGas(params: DEXTradeParams): Promise<GasEstimate>
  
  // 流动性操作
  buildAddLiquidityTransaction(params: LiquidityParams): Promise<any>
  buildRemoveLiquidityTransaction(params: LiquidityParams): Promise<any>
  
  // 闪电贷（如果支持）
  buildFlashLoanTransaction?(params: FlashLoanParams): Promise<any>
}

// 支持的DEX协议
export const SupportedDEX = z.enum([
  'uniswap-v2',
  'uniswap-v3', 
  'sushiswap',
  'pancakeswap',
  'curve',
  'balancer',
  '1inch',
  'dodo',
  'kyber',
  'bancor'
])
export type SupportedDEX = z.infer<typeof SupportedDEX>

// 支持的区块链
export const SupportedChain = z.enum([
  'ethereum',
  'bsc',
  'polygon',
  'arbitrum',
  'optimism',
  'avalanche',
  'fantom',
  'solana'
])
export type SupportedChain = z.infer<typeof SupportedChain>

// DEX错误类型
export class DEXError extends Error {
  constructor(
    message: string,
    public readonly protocol: string,
    public readonly code?: string,
    public readonly details?: any
  ) {
    super(message)
    this.name = 'DEXError'
  }
}

export class InsufficientLiquidityError extends DEXError {
  constructor(message: string, protocol: string, details?: any) {
    super(message, protocol, 'INSUFFICIENT_LIQUIDITY', details)
    this.name = 'InsufficientLiquidityError'
  }
}

export class SlippageExceededError extends DEXError {
  constructor(message: string, protocol: string, details?: any) {
    super(message, protocol, 'SLIPPAGE_EXCEEDED', details)
    this.name = 'SlippageExceededError'
  }
}

export class GasEstimationError extends DEXError {
  constructor(message: string, protocol: string, details?: any) {
    super(message, protocol, 'GAS_ESTIMATION_ERROR', details)
    this.name = 'GasEstimationError'
  }
}
