{"name": "@sfquant/dex-adapter", "version": "1.0.0", "description": "DEX adapter for unified exchange interface", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "dev": "tsc --watch", "clean": "rm -rf dist", "type-check": "tsc --noEmit"}, "dependencies": {"@sfquant/core": "workspace:*", "ethers": "^6.8.0", "viem": "^1.19.0", "@uniswap/v3-sdk": "^3.10.0", "@uniswap/sdk-core": "^4.2.0", "@uniswap/v2-sdk": "^3.2.0", "axios": "^1.6.0", "ws": "^8.14.0", "bignumber.js": "^9.1.0"}, "devDependencies": {"@types/node": "^20.10.0", "@types/ws": "^8.5.0", "typescript": "^5.3.0"}, "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}}}