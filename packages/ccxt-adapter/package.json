{"name": "@sfquant/ccxt-adapter", "version": "1.0.0", "description": "CCXT adapter for unified exchange interface", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "dev": "tsc --watch", "clean": "rm -rf dist", "type-check": "tsc --noEmit"}, "dependencies": {"@sfquant/core": "workspace:*", "ccxt": "^4.2.0", "ws": "^8.14.0"}, "devDependencies": {"@types/node": "^20.10.0", "@types/ws": "^8.5.0", "typescript": "^5.3.0"}, "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}}}