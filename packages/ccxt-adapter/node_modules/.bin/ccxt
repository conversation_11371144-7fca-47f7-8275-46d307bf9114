#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/Users/<USER>/CursorSpace/SFQuant/node_modules/.pnpm/ccxt@4.4.85/node_modules/ccxt/examples/js/node_modules:/Users/<USER>/CursorSpace/SFQuant/node_modules/.pnpm/ccxt@4.4.85/node_modules/ccxt/examples/node_modules:/Users/<USER>/CursorSpace/SFQuant/node_modules/.pnpm/ccxt@4.4.85/node_modules/ccxt/node_modules:/Users/<USER>/CursorSpace/SFQuant/node_modules/.pnpm/ccxt@4.4.85/node_modules:/Users/<USER>/CursorSpace/SFQuant/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/Users/<USER>/CursorSpace/SFQuant/node_modules/.pnpm/ccxt@4.4.85/node_modules/ccxt/examples/js/node_modules:/Users/<USER>/CursorSpace/SFQuant/node_modules/.pnpm/ccxt@4.4.85/node_modules/ccxt/examples/node_modules:/Users/<USER>/CursorSpace/SFQuant/node_modules/.pnpm/ccxt@4.4.85/node_modules/ccxt/node_modules:/Users/<USER>/CursorSpace/SFQuant/node_modules/.pnpm/ccxt@4.4.85/node_modules:/Users/<USER>/CursorSpace/SFQuant/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../ccxt/examples/js/cli.js" "$@"
else
  exec node  "$basedir/../ccxt/examples/js/cli.js" "$@"
fi
