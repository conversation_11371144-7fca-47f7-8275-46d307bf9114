import * as ccxt from 'ccxt'
import {
  UnifiedExchange,
  ExchangeConfig,
  UnifiedOrder,
  OrderResult,
  OrderBook,
  Balance,
  MarketData,
  ExchangeError,
  NetworkError,
  AuthenticationError,
  InsufficientFunds,
  InvalidOrder,
  RateLimitExceeded
} from '../../core/dist/index.js'

export class CCXTAdapter implements UnifiedExchange {
  private exchange: ccxt.Exchange | null = null
  private _id: string = ''
  private _name: string = ''
  private _type: 'cex' | 'dex' = 'cex'
  private subscriptions: Map<string, any> = new Map()

  get id(): string {
    return this._id
  }

  get name(): string {
    return this._name
  }

  get type(): 'cex' | 'dex' {
    return this._type
  }

  async initialize(config: ExchangeConfig): Promise<void> {
    try {
      this._id = config.id
      this._name = config.name
      this._type = config.type

      // 检查CCXT是否支持该交易所
      if (!(config.id in ccxt)) {
        throw new ExchangeError(`Exchange ${config.id} not supported by CCXT`, config.id)
      }

      // 创建交易所实例
      const ExchangeClass = ccxt[config.id as keyof typeof ccxt] as any
      this.exchange = new ExchangeClass({
        apiKey: config.apiKey,
        secret: config.secret,
        password: config.passphrase,
        sandbox: config.sandbox,
        rateLimit: config.rateLimit,
        enableRateLimit: true,
        ...config.options
      })

      // 加载市场信息
      await this.exchange!.loadMarkets()
    } catch (error) {
      throw this.handleError(error, config.id)
    }
  }

  isInitialized(): boolean {
    return this.exchange !== null && this.exchange.markets !== undefined
  }

  async fetchMarkets(): Promise<string[]> {
    this.ensureInitialized()
    try {
      const markets = await this.exchange!.fetchMarkets()
      return markets.map((market: any) => market.symbol)
    } catch (error) {
      throw this.handleError(error, this.id)
    }
  }

  async fetchTicker(symbol: string): Promise<MarketData> {
    this.ensureInitialized()
    try {
      const ticker = await this.exchange!.fetchTicker(symbol)
      return {
        symbol,
        timestamp: ticker.timestamp || Date.now(),
        bid: ticker.bid || 0,
        ask: ticker.ask || 0,
        last: ticker.last || 0,
        volume: ticker.baseVolume || 0,
        high: ticker.high || 0,
        low: ticker.low || 0,
        open: ticker.open || 0,
        close: ticker.close || ticker.last || 0
      }
    } catch (error) {
      throw this.handleError(error, this.id)
    }
  }

  async fetchOrderBook(symbol: string, limit?: number): Promise<OrderBook> {
    this.ensureInitialized()
    try {
      const orderbook = await this.exchange!.fetchOrderBook(symbol, limit)
      return {
        symbol,
        timestamp: orderbook.timestamp || Date.now(),
        bids: orderbook.bids.map((entry: any) => ({ price: Number(entry[0]), amount: Number(entry[1]) })),
        asks: orderbook.asks.map((entry: any) => ({ price: Number(entry[0]), amount: Number(entry[1]) }))
      }
    } catch (error) {
      throw this.handleError(error, this.id)
    }
  }

  async fetchTrades(symbol: string, limit?: number): Promise<any[]> {
    this.ensureInitialized()
    try {
      return await this.exchange!.fetchTrades(symbol, undefined, limit)
    } catch (error) {
      throw this.handleError(error, this.id)
    }
  }

  async fetchOHLCV(symbol: string, timeframe: string, since?: number, limit?: number): Promise<any[]> {
    this.ensureInitialized()
    try {
      return await this.exchange!.fetchOHLCV(symbol, timeframe, since, limit)
    } catch (error) {
      throw this.handleError(error, this.id)
    }
  }

  async fetchBalance(): Promise<Record<string, Balance>> {
    this.ensureInitialized()
    try {
      const balance = await this.exchange!.fetchBalance()
      const result: Record<string, Balance> = {}

      for (const [currency, info] of Object.entries(balance)) {
        if (currency !== 'info' && currency !== 'free' && currency !== 'used' && currency !== 'total') {
          const balanceInfo = info as any
          result[currency] = {
            currency,
            free: balanceInfo.free || 0,
            used: balanceInfo.used || 0,
            total: balanceInfo.total || 0
          }
        }
      }

      return result
    } catch (error) {
      throw this.handleError(error, this.id)
    }
  }

  async fetchOpenOrders(symbol?: string): Promise<OrderResult[]> {
    this.ensureInitialized()
    try {
      const orders = await this.exchange!.fetchOpenOrders(symbol)
      return orders.map((order: any) => this.transformOrder(order))
    } catch (error) {
      throw this.handleError(error, this.id)
    }
  }

  async fetchClosedOrders(symbol?: string, since?: number, limit?: number): Promise<OrderResult[]> {
    this.ensureInitialized()
    try {
      const orders = await this.exchange!.fetchClosedOrders(symbol, since, limit)
      return orders.map((order: any) => this.transformOrder(order))
    } catch (error) {
      throw this.handleError(error, this.id)
    }
  }

  async createOrder(order: UnifiedOrder): Promise<OrderResult> {
    this.ensureInitialized()
    try {
      const result = await this.exchange!.createOrder(
        order.symbol,
        order.type,
        order.side,
        order.amount,
        order.price,
        {
          stopPrice: order.stopPrice,
          timeInForce: order.timeInForce,
          clientOrderId: order.clientOrderId
        }
      )
      return this.transformOrder(result)
    } catch (error) {
      throw this.handleError(error, this.id)
    }
  }

  async cancelOrder(id: string, symbol?: string): Promise<OrderResult> {
    this.ensureInitialized()
    try {
      const result = await this.exchange!.cancelOrder(id, symbol)
      return this.transformOrder(result)
    } catch (error) {
      throw this.handleError(error, this.id)
    }
  }

  async cancelAllOrders(symbol?: string): Promise<OrderResult[]> {
    this.ensureInitialized()
    try {
      const orders = await this.exchange!.cancelAllOrders(symbol)
      return Array.isArray(orders) ? orders.map((order: any) => this.transformOrder(order)) : []
    } catch (error) {
      throw this.handleError(error, this.id)
    }
  }

  // WebSocket订阅方法
  async subscribeToTicker(symbol: string, callback: (data: MarketData) => void): Promise<void> {
    // 实现WebSocket ticker订阅
    // 这里需要根据具体交易所的WebSocket API实现
  }

  async subscribeToOrderBook(symbol: string, callback: (data: OrderBook) => void): Promise<void> {
    // 实现WebSocket orderbook订阅
  }

  async subscribeToTrades(symbol: string, callback: (data: any) => void): Promise<void> {
    // 实现WebSocket trades订阅
  }

  async unsubscribe(symbol: string, type: 'ticker' | 'orderbook' | 'trades'): Promise<void> {
    // 实现取消订阅
  }

  private ensureInitialized(): void {
    if (!this.isInitialized()) {
      throw new ExchangeError('Exchange not initialized', this.id)
    }
  }

  private transformOrder(order: any): OrderResult {
    return {
      id: order.id,
      clientOrderId: order.clientOrderId,
      symbol: order.symbol,
      side: order.side,
      type: order.type,
      amount: order.amount,
      price: order.price,
      filled: order.filled || 0,
      remaining: order.remaining || order.amount,
      status: order.status,
      timestamp: order.timestamp || Date.now(),
      fee: order.fee ? {
        currency: order.fee.currency,
        cost: order.fee.cost
      } : undefined
    }
  }

  private handleError(error: any, exchangeId: string): ExchangeError {
    if (error instanceof ccxt.NetworkError) {
      return new NetworkError(error.message, exchangeId, error)
    } else if (error instanceof ccxt.AuthenticationError) {
      return new AuthenticationError(error.message, exchangeId, error)
    } else if (error instanceof ccxt.InsufficientFunds) {
      return new InsufficientFunds(error.message, exchangeId, error)
    } else if (error instanceof ccxt.InvalidOrder) {
      return new InvalidOrder(error.message, exchangeId, error)
    } else if (error instanceof ccxt.RateLimitExceeded) {
      return new RateLimitExceeded(error.message, exchangeId, error)
    } else if (error instanceof ccxt.BaseError) {
      return new ExchangeError(error.message, exchangeId, error.constructor.name, error)
    } else {
      return new ExchangeError(error.message || 'Unknown error', exchangeId, 'UNKNOWN_ERROR', error)
    }
  }
}
