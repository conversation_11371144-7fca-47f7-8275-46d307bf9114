{"name": "@sfquant/core", "version": "1.0.0", "description": "Core types and utilities for SFQuant", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "dev": "tsc --watch", "clean": "rm -rf dist", "type-check": "tsc --noEmit"}, "dependencies": {"zod": "^3.22.4"}, "devDependencies": {"@types/node": "^20.10.0", "typescript": "^5.3.0"}, "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}}}