import { z } from 'zod'
import { UnifiedOrder, OrderResult, OrderBook, Balance, MarketData } from './strategy'

// 交易所配置
export const ExchangeConfig = z.object({
  id: z.string(),
  name: z.string(),
  type: z.enum(['cex', 'dex']),
  apiKey: z.string().optional(),
  secret: z.string().optional(),
  passphrase: z.string().optional(),
  sandbox: z.boolean().default(false),
  rateLimit: z.number().optional(),
  // DEX特有配置
  chainId: z.number().optional(),
  rpcUrl: z.string().optional(),
  privateKey: z.string().optional(),
  // 其他配置
  options: z.record(z.any()).optional()
})
export type ExchangeConfig = z.infer<typeof ExchangeConfig>

// 统一交易所接口
export interface UnifiedExchange {
  readonly id: string
  readonly name: string
  readonly type: 'cex' | 'dex'
  
  // 基础方法
  initialize(config: ExchangeConfig): Promise<void>
  isInitialized(): boolean
  
  // 市场数据
  fetchMarkets(): Promise<string[]>
  fetchTicker(symbol: string): Promise<MarketData>
  fetchOrderBook(symbol: string, limit?: number): Promise<OrderBook>
  fetchTrades(symbol: string, limit?: number): Promise<any[]>
  fetchOHLCV(symbol: string, timeframe: string, since?: number, limit?: number): Promise<any[]>
  
  // 账户信息
  fetchBalance(): Promise<Record<string, Balance>>
  fetchOpenOrders(symbol?: string): Promise<OrderResult[]>
  fetchClosedOrders(symbol?: string, since?: number, limit?: number): Promise<OrderResult[]>
  
  // 交易操作
  createOrder(order: UnifiedOrder): Promise<OrderResult>
  cancelOrder(id: string, symbol?: string): Promise<OrderResult>
  cancelAllOrders(symbol?: string): Promise<OrderResult[]>
  
  // 实时数据订阅
  subscribeToTicker(symbol: string, callback: (data: MarketData) => void): Promise<void>
  subscribeToOrderBook(symbol: string, callback: (data: OrderBook) => void): Promise<void>
  subscribeToTrades(symbol: string, callback: (data: any) => void): Promise<void>
  unsubscribe(symbol: string, type: 'ticker' | 'orderbook' | 'trades'): Promise<void>
  
  // DEX特有方法
  estimateGas?(transaction: any): Promise<bigint>
  getTokenPrice?(tokenAddress: string, baseToken?: string): Promise<number>
  getLiquidityPools?(tokenA: string, tokenB: string): Promise<any[]>
  
  // CEX特有方法
  fetchDepositAddress?(currency: string): Promise<any>
  fetchWithdrawals?(currency?: string, since?: number, limit?: number): Promise<any[]>
  fetchDeposits?(currency?: string, since?: number, limit?: number): Promise<any[]>
}

// 交易所能力
export const ExchangeCapabilities = z.object({
  trading: z.boolean(),
  fetchTicker: z.boolean(),
  fetchOrderBook: z.boolean(),
  fetchTrades: z.boolean(),
  fetchOHLCV: z.boolean(),
  fetchBalance: z.boolean(),
  fetchOpenOrders: z.boolean(),
  fetchClosedOrders: z.boolean(),
  createOrder: z.boolean(),
  cancelOrder: z.boolean(),
  cancelAllOrders: z.boolean(),
  // WebSocket支持
  ws: z.boolean(),
  watchTicker: z.boolean(),
  watchOrderBook: z.boolean(),
  watchTrades: z.boolean(),
  watchBalance: z.boolean(),
  watchOrders: z.boolean(),
  // DEX特有能力
  swapTokens: z.boolean().optional(),
  addLiquidity: z.boolean().optional(),
  removeLiquidity: z.boolean().optional(),
  flashLoan: z.boolean().optional()
})
export type ExchangeCapabilities = z.infer<typeof ExchangeCapabilities>

// 交易所信息
export const ExchangeInfo = z.object({
  id: z.string(),
  name: z.string(),
  type: z.enum(['cex', 'dex']),
  countries: z.array(z.string()).optional(),
  urls: z.object({
    logo: z.string().optional(),
    www: z.string().optional(),
    api: z.string().optional(),
    doc: z.string().optional()
  }).optional(),
  capabilities: ExchangeCapabilities,
  fees: z.object({
    trading: z.object({
      maker: z.number(),
      taker: z.number()
    }).optional(),
    funding: z.object({
      withdraw: z.record(z.number()),
      deposit: z.record(z.number())
    }).optional()
  }).optional(),
  limits: z.object({
    amount: z.object({
      min: z.number().optional(),
      max: z.number().optional()
    }).optional(),
    price: z.object({
      min: z.number().optional(),
      max: z.number().optional()
    }).optional(),
    cost: z.object({
      min: z.number().optional(),
      max: z.number().optional()
    }).optional()
  }).optional()
})
export type ExchangeInfo = z.infer<typeof ExchangeInfo>

// 交易所错误类型
export class ExchangeError extends Error {
  constructor(
    message: string,
    public readonly exchangeId: string,
    public readonly code?: string,
    public readonly details?: any
  ) {
    super(message)
    this.name = 'ExchangeError'
  }
}

export class NetworkError extends ExchangeError {
  constructor(message: string, exchangeId: string, details?: any) {
    super(message, exchangeId, 'NETWORK_ERROR', details)
    this.name = 'NetworkError'
  }
}

export class AuthenticationError extends ExchangeError {
  constructor(message: string, exchangeId: string, details?: any) {
    super(message, exchangeId, 'AUTH_ERROR', details)
    this.name = 'AuthenticationError'
  }
}

export class InsufficientFunds extends ExchangeError {
  constructor(message: string, exchangeId: string, details?: any) {
    super(message, exchangeId, 'INSUFFICIENT_FUNDS', details)
    this.name = 'InsufficientFunds'
  }
}

export class InvalidOrder extends ExchangeError {
  constructor(message: string, exchangeId: string, details?: any) {
    super(message, exchangeId, 'INVALID_ORDER', details)
    this.name = 'InvalidOrder'
  }
}

export class RateLimitExceeded extends ExchangeError {
  constructor(message: string, exchangeId: string, details?: any) {
    super(message, exchangeId, 'RATE_LIMIT', details)
    this.name = 'RateLimitExceeded'
  }
}
