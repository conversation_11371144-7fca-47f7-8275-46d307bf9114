import { z } from 'zod';
// 策略语言类型
export const StrategyLanguage = z.enum(['typescript', 'python', 'javascript']);
// 策略类型
export const StrategyType = z.enum([
    'arbitrage', // 套利策略
    'trend', // 趋势策略
    'high_frequency', // 高频策略
    'market_maker', // 做市商策略
    'ai_ml' // AI/ML策略
]);
// 交易所类型
export const ExchangeType = z.enum(['cex', 'dex']);
// 订单方向
export const OrderSide = z.enum(['buy', 'sell']);
// 订单类型
export const OrderType = z.enum(['market', 'limit', 'stop', 'stop_limit']);
// 订单状态
export const OrderStatus = z.enum(['pending', 'open', 'closed', 'canceled', 'rejected']);
// 市场数据
export const MarketData = z.object({
    symbol: z.string(),
    timestamp: z.number(),
    bid: z.number(),
    ask: z.number(),
    last: z.number(),
    volume: z.number(),
    high: z.number(),
    low: z.number(),
    open: z.number(),
    close: z.number()
});
// 订单簿
export const OrderBookEntry = z.object({
    price: z.number(),
    amount: z.number()
});
export const OrderBook = z.object({
    symbol: z.string(),
    timestamp: z.number(),
    bids: z.array(OrderBookEntry),
    asks: z.array(OrderBookEntry)
});
// 统一订单接口
export const UnifiedOrder = z.object({
    id: z.string().optional(),
    symbol: z.string(),
    side: OrderSide,
    type: OrderType,
    amount: z.number(),
    price: z.number().optional(),
    stopPrice: z.number().optional(),
    timeInForce: z.enum(['GTC', 'IOC', 'FOK']).optional(),
    clientOrderId: z.string().optional()
});
// 订单结果
export const OrderResult = z.object({
    id: z.string(),
    clientOrderId: z.string().optional(),
    symbol: z.string(),
    side: OrderSide,
    type: OrderType,
    amount: z.number(),
    price: z.number().optional(),
    filled: z.number(),
    remaining: z.number(),
    status: OrderStatus,
    timestamp: z.number(),
    fee: z.object({
        currency: z.string(),
        cost: z.number()
    }).optional()
});
// 账户余额
export const Balance = z.object({
    currency: z.string(),
    free: z.number(),
    used: z.number(),
    total: z.number()
});
// 策略信号
export const Signal = z.object({
    type: z.enum(['buy', 'sell', 'hold']),
    symbol: z.string(),
    amount: z.number().optional(),
    price: z.number().optional(),
    confidence: z.number().min(0).max(1).optional(),
    metadata: z.record(z.any()).optional()
});
// 策略配置
export const StrategyConfig = z.object({
    id: z.string(),
    name: z.string(),
    description: z.string().optional(),
    type: StrategyType,
    language: StrategyLanguage,
    code: z.string(),
    parameters: z.record(z.any()),
    exchanges: z.array(z.string()),
    symbols: z.array(z.string()),
    enabled: z.boolean().default(false),
    riskLimits: z.object({
        maxPositionSize: z.number().optional(),
        maxDailyLoss: z.number().optional(),
        maxDrawdown: z.number().optional()
    }).optional(),
    createdAt: z.number(),
    updatedAt: z.number()
});
// 策略执行上下文
export const StrategyContext = z.object({
    strategy: StrategyConfig,
    marketData: z.record(MarketData),
    orderBooks: z.record(OrderBook),
    balances: z.record(Balance),
    openOrders: z.array(OrderResult),
    timestamp: z.number()
});
// 策略执行结果
export const StrategyResult = z.object({
    signals: z.array(Signal),
    logs: z.array(z.string()).optional(),
    metrics: z.record(z.number()).optional(),
    error: z.string().optional()
});
//# sourceMappingURL=strategy.js.map