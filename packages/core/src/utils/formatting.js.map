{"version": 3, "file": "formatting.js", "sourceRoot": "", "sources": ["formatting.ts"], "names": [], "mappings": "AAAA,UAAU;AAEV,MAAM,UAAU,WAAW,CAAC,KAAa,EAAE,WAAmB,CAAC;IAC7D,OAAO,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAA;AAChC,CAAC;AAED,MAAM,UAAU,YAAY,CAAC,MAAc,EAAE,WAAmB,CAAC;IAC/D,OAAO,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAA;AACjC,CAAC;AAED,MAAM,UAAU,gBAAgB,CAAC,KAAa,EAAE,WAAmB,CAAC;IAClE,OAAO,GAAG,CAAC,KAAK,GAAG,GAAG,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAA;AAC9C,CAAC;AAED,MAAM,UAAU,cAAc,CAAC,MAAc,EAAE,WAAmB,KAAK,EAAE,WAAmB,CAAC;IAC3F,OAAO,GAAG,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,QAAQ,EAAE,CAAA;AAClD,CAAC;AAED,MAAM,UAAU,eAAe,CAAC,SAAiB;IAC/C,OAAO,IAAI,IAAI,CAAC,SAAS,CAAC,CAAC,WAAW,EAAE,CAAA;AAC1C,CAAC;AAED,MAAM,UAAU,cAAc,CAAC,YAAoB;IACjD,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,YAAY,GAAG,IAAI,CAAC,CAAA;IAC/C,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,GAAG,EAAE,CAAC,CAAA;IACxC,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,GAAG,EAAE,CAAC,CAAA;IACtC,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,EAAE,CAAC,CAAA;IAEnC,IAAI,IAAI,GAAG,CAAC,EAAE,CAAC;QACb,OAAO,GAAG,IAAI,KAAK,KAAK,GAAG,EAAE,KAAK,OAAO,GAAG,EAAE,GAAG,CAAA;IACnD,CAAC;SAAM,IAAI,KAAK,GAAG,CAAC,EAAE,CAAC;QACrB,OAAO,GAAG,KAAK,KAAK,OAAO,GAAG,EAAE,KAAK,OAAO,GAAG,EAAE,GAAG,CAAA;IACtD,CAAC;SAAM,IAAI,OAAO,GAAG,CAAC,EAAE,CAAC;QACvB,OAAO,GAAG,OAAO,KAAK,OAAO,GAAG,EAAE,GAAG,CAAA;IACvC,CAAC;SAAM,CAAC;QACN,OAAO,GAAG,OAAO,GAAG,CAAA;IACtB,CAAC;AACH,CAAC;AAED,MAAM,UAAU,eAAe,CAAC,OAAe,EAAE,aAAqB,CAAC,EAAE,WAAmB,CAAC;IAC3F,IAAI,OAAO,CAAC,MAAM,IAAI,UAAU,GAAG,QAAQ,EAAE,CAAC;QAC5C,OAAO,OAAO,CAAA;IAChB,CAAC;IACD,OAAO,GAAG,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE,UAAU,CAAC,MAAM,OAAO,CAAC,KAAK,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAA;AACxE,CAAC;AAED,MAAM,UAAU,YAAY,CAAC,GAAW;IACtC,IAAI,GAAG,IAAI,GAAG,EAAE,CAAC;QACf,OAAO,GAAG,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAA;IACrC,CAAC;SAAM,IAAI,GAAG,IAAI,GAAG,EAAE,CAAC;QACtB,OAAO,GAAG,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAA;IACrC,CAAC;SAAM,IAAI,GAAG,IAAI,GAAG,EAAE,CAAC;QACtB,OAAO,GAAG,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAA;IACrC,CAAC;SAAM,CAAC;QACN,OAAO,GAAG,CAAC,QAAQ,EAAE,CAAA;IACvB,CAAC;AACH,CAAC"}