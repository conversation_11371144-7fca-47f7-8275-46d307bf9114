// 验证工具函数
export function isValidSymbol(symbol) {
    // 验证交易对格式，例如 BTC/USDT
    const symbolRegex = /^[A-Z0-9]+\/[A-Z0-9]+$/;
    return symbolRegex.test(symbol);
}
export function isValidAmount(amount) {
    return amount > 0 && Number.isFinite(amount);
}
export function isValidPrice(price) {
    return price > 0 && Number.isFinite(price);
}
export function isValidAddress(address) {
    // 简单的以太坊地址验证
    const ethAddressRegex = /^0x[a-fA-F0-9]{40}$/;
    return ethAddressRegex.test(address);
}
export function sanitizeString(input) {
    // 清理字符串，移除潜在的危险字符
    return input.replace(/[<>\"'&]/g, '');
}
export function validateEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}
//# sourceMappingURL=validation.js.map