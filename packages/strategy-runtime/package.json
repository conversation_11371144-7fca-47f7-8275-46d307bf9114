{"name": "@sfquant/strategy-runtime", "version": "1.0.0", "description": "Multi-language strategy runtime for SFQuant", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "dev": "tsc --watch", "clean": "rm -rf dist", "type-check": "tsc --noEmit"}, "dependencies": {"@sfquant/core": "workspace:*", "vm2": "^3.9.19", "ts-node": "^10.9.0", "typescript": "^5.3.0"}, "devDependencies": {"@types/node": "^20.10.0"}, "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}}}