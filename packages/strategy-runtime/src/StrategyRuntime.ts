import {
  StrategyLanguage,
  StrategyContext,
  StrategyResult,
  Signal
} from '../../core/dist/index.js'

// 策略运行时接口
export interface StrategyRuntime {
  readonly language: StrategyLanguage
  execute(code: string, context: StrategyContext): Promise<StrategyResult>
  validate(code: string): Promise<ValidationResult>
  dispose(): Promise<void>
}

// 验证结果
export interface ValidationResult {
  valid: boolean
  errors: string[]
  warnings: string[]
}

// 基础策略运行时
export abstract class BaseStrategyRuntime implements StrategyRuntime {
  abstract readonly language: StrategyLanguage

  abstract execute(code: string, context: StrategyContext): Promise<StrategyResult>
  abstract validate(code: string): Promise<ValidationResult>
  abstract dispose(): Promise<void>

  protected createSafeContext(context: StrategyContext) {
    return {
      // 市场数据
      marketData: context.marketData,
      orderBooks: context.orderBooks,
      balances: context.balances,
      openOrders: context.openOrders,
      timestamp: context.timestamp,

      // 策略配置
      strategy: {
        id: context.strategy.id,
        name: context.strategy.name,
        type: context.strategy.type,
        parameters: context.strategy.parameters,
        exchanges: context.strategy.exchanges,
        symbols: context.strategy.symbols
      },

      // 工具函数
      utils: {
        log: (message: string) => console.log(`[${context.strategy.name}] ${message}`),
        warn: (message: string) => console.warn(`[${context.strategy.name}] ${message}`),
        error: (message: string) => console.error(`[${context.strategy.name}] ${message}`)
      },

      // 技术指标库 (简化版)
      indicators: {
        sma: (prices: number[], period: number) => this.calculateSMA(prices, period),
        ema: (prices: number[], period: number) => this.calculateEMA(prices, period),
        rsi: (prices: number[], period: number) => this.calculateRSI(prices, period),
        macd: (prices: number[], fast: number, slow: number, signal: number) =>
          this.calculateMACD(prices, fast, slow, signal)
      }
    }
  }

  protected createSignal(
    type: 'buy' | 'sell' | 'hold',
    symbol: string,
    amount?: number,
    price?: number,
    confidence?: number,
    metadata?: Record<string, any>
  ): Signal {
    return {
      type,
      symbol,
      amount,
      price,
      confidence,
      metadata
    }
  }

  // 简化的技术指标实现
  private calculateSMA(prices: number[], period: number): number[] {
    const result: number[] = []
    for (let i = period - 1; i < prices.length; i++) {
      const sum = prices.slice(i - period + 1, i + 1).reduce((a, b) => a + b, 0)
      result.push(sum / period)
    }
    return result
  }

  private calculateEMA(prices: number[], period: number): number[] {
    const result: number[] = []
    const multiplier = 2 / (period + 1)

    // 第一个值使用SMA
    let ema = prices.slice(0, period).reduce((a, b) => a + b, 0) / period
    result.push(ema)

    // 后续值使用EMA公式
    for (let i = period; i < prices.length; i++) {
      ema = (prices[i] - ema) * multiplier + ema
      result.push(ema)
    }

    return result
  }

  private calculateRSI(prices: number[], period: number): number[] {
    const result: number[] = []
    const gains: number[] = []
    const losses: number[] = []

    // 计算价格变化
    for (let i = 1; i < prices.length; i++) {
      const change = prices[i] - prices[i - 1]
      gains.push(change > 0 ? change : 0)
      losses.push(change < 0 ? -change : 0)
    }

    // 计算RSI
    for (let i = period - 1; i < gains.length; i++) {
      const avgGain = gains.slice(i - period + 1, i + 1).reduce((a, b) => a + b, 0) / period
      const avgLoss = losses.slice(i - period + 1, i + 1).reduce((a, b) => a + b, 0) / period

      if (avgLoss === 0) {
        result.push(100)
      } else {
        const rs = avgGain / avgLoss
        const rsi = 100 - (100 / (1 + rs))
        result.push(rsi)
      }
    }

    return result
  }

  private calculateMACD(prices: number[], fast: number, slow: number, signal: number): {
    macd: number[]
    signal: number[]
    histogram: number[]
  } {
    const emaFast = this.calculateEMA(prices, fast)
    const emaSlow = this.calculateEMA(prices, slow)

    // 计算MACD线
    const macd: number[] = []
    const startIndex = slow - fast
    for (let i = startIndex; i < emaFast.length; i++) {
      macd.push(emaFast[i] - emaSlow[i - startIndex])
    }

    // 计算信号线
    const signalLine = this.calculateEMA(macd, signal)

    // 计算柱状图
    const histogram: number[] = []
    const signalStartIndex = signal - 1
    for (let i = signalStartIndex; i < macd.length; i++) {
      histogram.push(macd[i] - signalLine[i - signalStartIndex])
    }

    return {
      macd,
      signal: signalLine,
      histogram
    }
  }
}

// 策略运行时管理器
export class StrategyRuntimeManager {
  private runtimes: Map<StrategyLanguage, StrategyRuntime> = new Map()

  registerRuntime(runtime: StrategyRuntime): void {
    this.runtimes.set(runtime.language, runtime)
  }

  getRuntime(language: StrategyLanguage): StrategyRuntime | undefined {
    return this.runtimes.get(language)
  }

  async executeStrategy(
    language: StrategyLanguage,
    code: string,
    context: StrategyContext
  ): Promise<StrategyResult> {
    const runtime = this.getRuntime(language)
    if (!runtime) {
      throw new Error(`Runtime for language ${language} not found`)
    }

    try {
      return await runtime.execute(code, context)
    } catch (error) {
      return {
        signals: [],
        error: error instanceof Error ? error.message : String(error)
      }
    }
  }

  async validateStrategy(language: StrategyLanguage, code: string): Promise<ValidationResult> {
    const runtime = this.getRuntime(language)
    if (!runtime) {
      return {
        valid: false,
        errors: [`Runtime for language ${language} not found`],
        warnings: []
      }
    }

    return await runtime.validate(code)
  }

  async dispose(): Promise<void> {
    const promises = Array.from(this.runtimes.values()).map(runtime => runtime.dispose())
    await Promise.all(promises)
    this.runtimes.clear()
  }
}
