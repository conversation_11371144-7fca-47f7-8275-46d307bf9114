import { VM } from 'vm2';
import * as ts from 'typescript';
import { BaseStrategyRuntime } from './StrategyRuntime';
export class TypeScriptRuntime extends BaseStrategyRuntime {
    constructor() {
        super();
        this.language = 'typescript';
        this.compilerOptions = {
            target: ts.ScriptTarget.ES2020,
            module: ts.ModuleKind.CommonJS,
            strict: true,
            esModuleInterop: true,
            skipLibCheck: true,
            forceConsistentCasingInFileNames: true,
            noEmit: true
        };
    }
    async execute(code, context) {
        try {
            // 编译TypeScript代码
            const compiledCode = this.compileTypeScript(code);
            // 创建安全的执行环境
            const safeContext = this.createSafeContext(context);
            const signals = [];
            const logs = [];
            // 增强上下文，添加信号创建函数
            const enhancedContext = {
                ...safeContext,
                createSignal: (type, symbol, amount, price, confidence, metadata) => {
                    const signal = this.createSignal(type, symbol, amount, price, confidence, metadata);
                    signals.push(signal);
                    return signal;
                },
                utils: {
                    ...safeContext.utils,
                    log: (message) => {
                        logs.push(message);
                        console.log(`[${context.strategy.name}] ${message}`);
                    }
                }
            };
            // 在VM中执行代码
            const vm = new VM({
                timeout: 30000, // 30秒超时
                sandbox: enhancedContext,
                allowAsync: true
            });
            // 包装代码以支持策略函数
            const wrappedCode = `
        ${compiledCode}
        
        // 如果定义了onTick函数，则调用它
        if (typeof onTick === 'function') {
          const result = onTick(marketData, orderBooks, balances, openOrders);
          if (result && typeof result.then === 'function') {
            result; // 返回Promise
          } else {
            Promise.resolve(result);
          }
        } else {
          Promise.resolve();
        }
      `;
            await vm.run(wrappedCode);
            return {
                signals,
                logs,
                metrics: this.calculateMetrics(signals)
            };
        }
        catch (error) {
            return {
                signals: [],
                error: error instanceof Error ? error.message : String(error)
            };
        }
    }
    async validate(code) {
        const errors = [];
        const warnings = [];
        try {
            // TypeScript编译检查
            const result = ts.transpileModule(code, {
                compilerOptions: this.compilerOptions,
                reportDiagnostics: true
            });
            // 收集编译错误和警告
            if (result.diagnostics) {
                for (const diagnostic of result.diagnostics) {
                    const message = ts.flattenDiagnosticMessageText(diagnostic.messageText, '\n');
                    if (diagnostic.category === ts.DiagnosticCategory.Error) {
                        errors.push(message);
                    }
                    else if (diagnostic.category === ts.DiagnosticCategory.Warning) {
                        warnings.push(message);
                    }
                }
            }
            // 语法检查
            this.validateStrategySyntax(code, errors, warnings);
        }
        catch (error) {
            errors.push(error instanceof Error ? error.message : String(error));
        }
        return {
            valid: errors.length === 0,
            errors,
            warnings
        };
    }
    async dispose() {
        // TypeScript运行时不需要特殊清理
    }
    compileTypeScript(code) {
        const result = ts.transpile(code, this.compilerOptions);
        return result;
    }
    validateStrategySyntax(code, errors, warnings) {
        // 检查必要的函数定义
        if (!code.includes('function onTick') && !code.includes('onTick =') && !code.includes('const onTick')) {
            warnings.push('Strategy should define an onTick function');
        }
        // 检查危险操作
        const dangerousPatterns = [
            /require\s*\(/g,
            /import\s+.*\s+from/g,
            /process\./g,
            /global\./g,
            /eval\s*\(/g,
            /Function\s*\(/g
        ];
        for (const pattern of dangerousPatterns) {
            if (pattern.test(code)) {
                errors.push(`Dangerous operation detected: ${pattern.source}`);
            }
        }
        // 检查策略结构
        if (code.length > 50000) {
            warnings.push('Strategy code is very long, consider splitting into smaller functions');
        }
    }
    calculateMetrics(signals) {
        const metrics = {
            totalSignals: signals.length,
            buySignals: signals.filter(s => s.type === 'buy').length,
            sellSignals: signals.filter(s => s.type === 'sell').length,
            holdSignals: signals.filter(s => s.type === 'hold').length
        };
        // 计算平均置信度
        const confidenceSignals = signals.filter(s => s.confidence !== undefined);
        if (confidenceSignals.length > 0) {
            metrics.averageConfidence = confidenceSignals.reduce((sum, s) => sum + (s.confidence || 0), 0) / confidenceSignals.length;
        }
        return metrics;
    }
}
// 策略模板
export const TYPESCRIPT_STRATEGY_TEMPLATE = `
/**
 * TypeScript策略模板
 * 
 * 可用的全局变量:
 * - marketData: 市场数据
 * - orderBooks: 订单簿数据
 * - balances: 账户余额
 * - openOrders: 未完成订单
 * - strategy: 策略配置
 * - utils: 工具函数 (log, warn, error)
 * - indicators: 技术指标 (sma, ema, rsi, macd)
 * - createSignal: 创建交易信号函数
 */

interface MarketData {
  symbol: string;
  timestamp: number;
  bid: number;
  ask: number;
  last: number;
  volume: number;
  high: number;
  low: number;
  open: number;
  close: number;
}

interface Signal {
  type: 'buy' | 'sell' | 'hold';
  symbol: string;
  amount?: number;
  price?: number;
  confidence?: number;
  metadata?: Record<string, any>;
}

// 主策略函数 - 每次市场数据更新时调用
function onTick(
  marketData: Record<string, MarketData>,
  orderBooks: Record<string, any>,
  balances: Record<string, any>,
  openOrders: any[]
): void {
  // 获取策略参数
  const symbol = strategy.symbols[0]; // 第一个交易对
  const data = marketData[symbol];
  
  if (!data) {
    utils.warn(\`No market data for \${symbol}\`);
    return;
  }
  
  // 示例: 简单的移动平均策略
  const prices = [data.close]; // 这里应该是历史价格数组
  const shortMA = indicators.sma(prices, 5);
  const longMA = indicators.sma(prices, 20);
  
  if (shortMA.length > 0 && longMA.length > 0) {
    const currentShortMA = shortMA[shortMA.length - 1];
    const currentLongMA = longMA[longMA.length - 1];
    
    if (currentShortMA > currentLongMA) {
      // 短期均线上穿长期均线，买入信号
      createSignal('buy', symbol, 0.1, data.ask, 0.8, {
        shortMA: currentShortMA,
        longMA: currentLongMA,
        reason: 'MA crossover bullish'
      });
      utils.log(\`Buy signal for \${symbol} at \${data.ask}\`);
    } else if (currentShortMA < currentLongMA) {
      // 短期均线下穿长期均线，卖出信号
      createSignal('sell', symbol, 0.1, data.bid, 0.8, {
        shortMA: currentShortMA,
        longMA: currentLongMA,
        reason: 'MA crossover bearish'
      });
      utils.log(\`Sell signal for \${symbol} at \${data.bid}\`);
    }
  }
}
`;
//# sourceMappingURL=TypeScriptRuntime.js.map