
> @sfquant/strategy-runtime@1.0.0 build /Users/<USER>/CursorSpace/SFQuant/packages/strategy-runtime
> tsc

[96m../core/src/index.ts[0m:[93m2[0m:[93m15[0m - [91merror[0m[90m TS6059: [0mFile '/Users/<USER>/CursorSpace/SFQuant/packages/core/src/types/strategy.ts' is not under 'rootDir' '/Users/<USER>/CursorSpace/SFQuant/packages/strategy-runtime/src'. 'rootDir' is expected to contain all source files.
  The file is in the program because:
    Imported via './types/strategy' from file '/Users/<USER>/CursorSpace/SFQuant/packages/core/src/index.ts'
    Imported via './strategy' from file '/Users/<USER>/CursorSpace/SFQuant/packages/core/src/types/exchange.ts'

[7m2[0m export * from './types/strategy'
[7m [0m [91m              ~~~~~~~~~~~~~~~~~~[0m

  [96m../core/src/types/exchange.ts[0m:[93m2[0m:[93m75[0m
    [7m2[0m import { UnifiedOrder, OrderResult, OrderBook, Balance, MarketData } from './strategy'
    [7m [0m [96m                                                                          ~~~~~~~~~~~~[0m
    File is included via import here.

[96m../core/src/index.ts[0m:[93m3[0m:[93m15[0m - [91merror[0m[90m TS6059: [0mFile '/Users/<USER>/CursorSpace/SFQuant/packages/core/src/types/exchange.ts' is not under 'rootDir' '/Users/<USER>/CursorSpace/SFQuant/packages/strategy-runtime/src'. 'rootDir' is expected to contain all source files.

[7m3[0m export * from './types/exchange'
[7m [0m [91m              ~~~~~~~~~~~~~~~~~~[0m

[96m../core/src/index.ts[0m:[93m6[0m:[93m15[0m - [91merror[0m[90m TS6059: [0mFile '/Users/<USER>/CursorSpace/SFQuant/packages/core/src/utils/validation.ts' is not under 'rootDir' '/Users/<USER>/CursorSpace/SFQuant/packages/strategy-runtime/src'. 'rootDir' is expected to contain all source files.

[7m6[0m export * from './utils/validation'
[7m [0m [91m              ~~~~~~~~~~~~~~~~~~~~[0m

[96m../core/src/index.ts[0m:[93m7[0m:[93m15[0m - [91merror[0m[90m TS6059: [0mFile '/Users/<USER>/CursorSpace/SFQuant/packages/core/src/utils/formatting.ts' is not under 'rootDir' '/Users/<USER>/CursorSpace/SFQuant/packages/strategy-runtime/src'. 'rootDir' is expected to contain all source files.

[7m7[0m export * from './utils/formatting'
[7m [0m [91m              ~~~~~~~~~~~~~~~~~~~~[0m

[96msrc/StrategyRuntime.ts[0m:[93m6[0m:[93m8[0m - [91merror[0m[90m TS6059: [0mFile '/Users/<USER>/CursorSpace/SFQuant/packages/core/src/index.ts' is not under 'rootDir' '/Users/<USER>/CursorSpace/SFQuant/packages/strategy-runtime/src'. 'rootDir' is expected to contain all source files.
  The file is in the program because:
    Imported via '@sfquant/core' from file '/Users/<USER>/CursorSpace/SFQuant/packages/strategy-runtime/src/StrategyRuntime.ts'
    Imported via '@sfquant/core' from file '/Users/<USER>/CursorSpace/SFQuant/packages/strategy-runtime/src/TypeScriptRuntime.ts'

[7m6[0m } from '@sfquant/core'
[7m [0m [91m       ~~~~~~~~~~~~~~~[0m

  [96msrc/TypeScriptRuntime.ts[0m:[93m8[0m:[93m8[0m
    [7m8[0m } from '@sfquant/core'
    [7m [0m [96m       ~~~~~~~~~~~~~~~[0m
    File is included via import here.


Found 5 errors in 2 files.

Errors  Files
     4  ../core/src/index.ts[90m:2[0m
     1  src/StrategyRuntime.ts[90m:6[0m
[41m[30m ELIFECYCLE [39m[49m [31mCommand failed with exit code 2.[39m
